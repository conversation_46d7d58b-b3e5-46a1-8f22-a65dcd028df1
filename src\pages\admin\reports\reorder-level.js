const { ipc<PERSON>ender<PERSON> } = require('electron');

// Global variables
let reorderData = null;
let lastUpdated = null;

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    console.log('📦 Reorder Level page loaded');
    loadReorderData();
});

// Load reorder data from backend
async function loadReorderData() {
    try {
        showLoading(true);
        
        console.log('📦 Fetching reorder alerts...');
        const result = await ipcRenderer.invoke('get-reorder-alerts');
        
        if (result.success) {
            reorderData = result.data;
            lastUpdated = new Date();
            
            console.log('📦 Reorder data loaded:', reorderData);
            
            displayStats(reorderData);
            displayReorderTable(reorderData.alerts);
            updateLastUpdatedTime();
            
            showLoading(false);
        } else {
            console.error('❌ Failed to load reorder data:', result.message);
            showError('Failed to load reorder data: ' + result.message);
        }
        
    } catch (error) {
        console.error('❌ Error loading reorder data:', error);
        showError('Error loading reorder data: ' + error.message);
    }
}

// Display statistics
function displayStats(data) {
    const statsGrid = document.getElementById('stats-grid');
    
    const criticalCount = data.critical_stock.length;
    const lowCount = data.low_stock.length;
    const totalValue = data.total_reorder_value || 0;
    const totalProducts = data.total_products_checked || 0;
    
    statsGrid.innerHTML = `
        <div class="stat-card">
            <div class="stat-number critical">${criticalCount}</div>
            <div class="stat-label">Critical Stock</div>
        </div>
        <div class="stat-card">
            <div class="stat-number warning">${lowCount}</div>
            <div class="stat-label">Low Stock</div>
        </div>
        <div class="stat-card">
            <div class="stat-number info">$${totalValue.toFixed(2)}</div>
            <div class="stat-label">Reorder Value</div>
        </div>
        <div class="stat-card">
            <div class="stat-number success">${totalProducts}</div>
            <div class="stat-label">Total Products</div>
        </div>
    `;
}

// Display reorder table
function displayReorderTable(alerts) {
    const table = document.getElementById('reorder-table');
    const tbody = document.getElementById('reorder-tbody');
    const emptyState = document.getElementById('empty-state');
    
    if (!alerts || alerts.length === 0) {
        table.style.display = 'none';
        emptyState.style.display = 'block';
        return;
    }
    
    emptyState.style.display = 'none';
    table.style.display = 'table';
    
    tbody.innerHTML = alerts.map(item => `
        <tr>
            <td>
                <strong>${item.description}</strong><br>
                <small style="color: #6c757d;">Barcode: ${item.barcode}</small>
            </td>
            <td>${item.category || 'N/A'}</td>
            <td>${item.location || 'N/A'}</td>
            <td>
                <strong style="color: ${getStockColor(item.stock, item.min_qty)}">${item.stock}</strong>
            </td>
            <td>${item.min_qty}</td>
            <td>${item.max_qty}</td>
            <td>
                <span class="status-badge status-${item.stock_status}">
                    ${item.stock_status.toUpperCase()}
                </span>
            </td>
            <td>
                <strong style="color: #007bff;">${item.reorder_quantity}</strong>
            </td>
            <td>
                ${item.days_until_stockout !== null ? 
                    `<span style="color: ${item.days_until_stockout <= 7 ? '#dc3545' : '#28a745'}">${item.days_until_stockout} days</span>` : 
                    '<span style="color: #6c757d;">Unknown</span>'
                }
            </td>
            <td>
                <button class="btn btn-primary" onclick="updateMinQty(${item.id})" style="font-size: 0.8rem; padding: 6px 12px;">
                    Edit Min
                </button>
                <button class="btn btn-success" onclick="addStock(${item.id})" style="font-size: 0.8rem; padding: 6px 12px; margin-left: 5px;">
                    Add Stock
                </button>
            </td>
        </tr>
    `).join('');
}

// Get color for stock level
function getStockColor(stock, minQty) {
    if (stock <= minQty) return '#dc3545'; // Critical - red
    if (stock <= minQty + 5) return '#fd7e14'; // Low - orange
    return '#28a745'; // Good - green
}

// Show/hide loading state
function showLoading(show) {
    const loading = document.getElementById('loading');
    const table = document.getElementById('reorder-table');
    const emptyState = document.getElementById('empty-state');
    
    if (show) {
        loading.style.display = 'block';
        table.style.display = 'none';
        emptyState.style.display = 'none';
    } else {
        loading.style.display = 'none';
    }
}

// Show error message
function showError(message) {
    showLoading(false);
    const loading = document.getElementById('loading');
    loading.innerHTML = `
        <div style="color: #dc3545;">
            <h3>❌ Error</h3>
            <p>${message}</p>
            <button class="btn btn-primary" onclick="loadReorderData()" style="margin-top: 15px;">
                Try Again
            </button>
        </div>
    `;
    loading.style.display = 'block';
}

// Update last updated time
function updateLastUpdatedTime() {
    const lastUpdatedEl = document.getElementById('last-updated');
    if (lastUpdated) {
        lastUpdatedEl.textContent = `Last updated: ${lastUpdated.toLocaleString()}`;
    }
}

// Refresh data
async function refreshData() {
    console.log('🔄 Refreshing reorder data...');
    await loadReorderData();
}

// Check all levels
async function checkAllLevels() {
    try {
        console.log('🔍 Checking all reorder levels...');
        
        const result = await ipcRenderer.invoke('check-reorder-levels');
        
        if (result.success) {
            console.log('✅ Reorder levels checked');
            await loadReorderData(); // Reload data
        } else {
            alert('Failed to check reorder levels: ' + result.message);
        }
        
    } catch (error) {
        console.error('❌ Error checking reorder levels:', error);
        alert('Error checking reorder levels: ' + error.message);
    }
}

// Update minimum quantity
async function updateMinQty(productId) {
    const newMinQty = prompt('Enter new minimum quantity:');
    
    if (newMinQty === null || newMinQty === '') return;
    
    const qty = parseInt(newMinQty);
    if (isNaN(qty) || qty < 0) {
        alert('Please enter a valid number');
        return;
    }
    
    try {
        console.log(`📦 Updating min quantity for product ${productId} to ${qty}`);
        
        const result = await ipcRenderer.invoke('update-min-quantity', productId, qty);
        
        if (result.success) {
            console.log('✅ Min quantity updated');
            await loadReorderData(); // Reload data
        } else {
            alert('Failed to update minimum quantity: ' + result.message);
        }
        
    } catch (error) {
        console.error('❌ Error updating min quantity:', error);
        alert('Error updating minimum quantity: ' + error.message);
    }
}

// Add stock
async function addStock(productId) {
    const quantity = prompt('Enter quantity to add to stock:');
    
    if (quantity === null || quantity === '') return;
    
    const qty = parseInt(quantity);
    if (isNaN(qty) || qty <= 0) {
        alert('Please enter a valid positive number');
        return;
    }
    
    try {
        console.log(`📦 Adding ${qty} stock for product ${productId}`);
        
        const result = await ipcRenderer.invoke('add-stock', productId, qty, 'manual_restock');
        
        if (result.success) {
            console.log('✅ Stock added');
            await loadReorderData(); // Reload data
        } else {
            alert('Failed to add stock: ' + result.message);
        }
        
    } catch (error) {
        console.error('❌ Error adding stock:', error);
        alert('Error adding stock: ' + error.message);
    }
}

// Export report
function exportReport() {
    if (!reorderData) {
        alert('No data to export');
        return;
    }
    
    try {
        // Create CSV content
        const csvContent = generateCSVReport(reorderData);
        
        // Create download link
        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `reorder-report-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
        
        console.log('📊 Report exported successfully');
        
    } catch (error) {
        console.error('❌ Error exporting report:', error);
        alert('Error exporting report: ' + error.message);
    }
}

// Generate CSV report
function generateCSVReport(data) {
    const headers = [
        'Product Name', 'Barcode', 'Category', 'Location', 
        'Current Stock', 'Min Qty', 'Max Qty', 'Status', 
        'Reorder Qty', 'Days Until Stockout', 'Last Checked'
    ];
    
    const rows = data.alerts.map(item => [
        `"${item.description}"`,
        item.barcode,
        item.category || '',
        item.location || '',
        item.stock,
        item.min_qty,
        item.max_qty,
        item.stock_status,
        item.reorder_quantity,
        item.days_until_stockout || '',
        item.last_checked
    ]);
    
    return [headers.join(','), ...rows.map(row => row.join(','))].join('\n');
}

// Go back to reports
function goBack() {
    window.history.back();
}
