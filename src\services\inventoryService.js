/**
 * Inventory Management Service
 * Handles stock level tracking, reorder alerts, and inventory updates
 */

class InventoryService {
    constructor(database) {
        this.db = database;
        this.reorderAlerts = [];
        this.lowStockThreshold = 5; // Default threshold for low stock warnings
        
        console.log('📦 Inventory Service initialized');
    }

    /**
     * Update inventory after a sale
     * Reduces stock levels for sold items
     */
    async updateInventoryAfterSale(saleItems, locationName) {
        try {
            console.log(`📦 Updating inventory for ${saleItems.length} items at ${locationName}`);
            
            const updatePromises = saleItems.map(async (item) => {
                if (item.productId) {
                    await this.reduceStock(item.productId, item.quantity, locationName);
                }
            });
            
            await Promise.all(updatePromises);
            
            // Check for reorder alerts after inventory update
            await this.checkReorderLevels(locationName);
            
            console.log('✅ Inventory updated successfully');
            return { success: true };
            
        } catch (error) {
            console.error('❌ Error updating inventory:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Reduce stock for a specific product at a location
     */
    async reduceStock(productId, quantity, locationName) {
        return new Promise((resolve, reject) => {
            const query = `
                UPDATE location_stocks 
                SET stock = stock - ?, updated_at = CURRENT_TIMESTAMP
                WHERE product_id = ? AND location = ?
            `;
            
            this.db.db.run(query, [quantity, productId, locationName], function(err) {
                if (err) {
                    console.error(`❌ Error reducing stock for product ${productId}:`, err);
                    reject(err);
                } else {
                    console.log(`📦 Reduced stock by ${quantity} for product ${productId} at ${locationName}`);
                    resolve({ changes: this.changes });
                }
            });
        });
    }

    /**
     * Check reorder levels for all products at a location
     */
    async checkReorderLevels(locationName = null) {
        try {
            console.log('🔍 Checking reorder levels...');
            
            const query = `
                SELECT 
                    p.id,
                    p.barcode,
                    p.description,
                    p.category,
                    p.subcategory,
                    p.supplier,
                    p.min_qty,
                    p.max_qty,
                    ls.location,
                    ls.stock,
                    ls.price,
                    CASE 
                        WHEN ls.stock <= p.min_qty THEN 'critical'
                        WHEN ls.stock <= (p.min_qty + ${this.lowStockThreshold}) THEN 'low'
                        ELSE 'normal'
                    END as stock_status
                FROM products p
                LEFT JOIN location_stocks ls ON p.id = ls.product_id
                WHERE p.min_qty > 0 
                ${locationName ? 'AND ls.location = ?' : ''}
                AND (ls.stock <= p.min_qty OR ls.stock <= (p.min_qty + ${this.lowStockThreshold}))
                ORDER BY 
                    CASE 
                        WHEN ls.stock <= p.min_qty THEN 1
                        ELSE 2
                    END,
                    ls.stock ASC
            `;
            
            const params = locationName ? [locationName] : [];
            
            return new Promise((resolve, reject) => {
                this.db.db.all(query, params, (err, rows) => {
                    if (err) {
                        console.error('❌ Error checking reorder levels:', err);
                        reject(err);
                    } else {
                        this.reorderAlerts = rows.map(row => ({
                            ...row,
                            reorder_quantity: Math.max(0, row.max_qty - row.stock),
                            days_until_stockout: this.calculateDaysUntilStockout(row.stock, row.id),
                            last_checked: new Date().toISOString()
                        }));
                        
                        console.log(`📊 Found ${this.reorderAlerts.length} products needing reorder`);
                        resolve(this.reorderAlerts);
                    }
                });
            });
            
        } catch (error) {
            console.error('❌ Error in checkReorderLevels:', error);
            throw error;
        }
    }

    /**
     * Get current reorder alerts
     */
    getReorderAlerts() {
        return this.reorderAlerts;
    }

    /**
     * Get detailed reorder report
     */
    async getReorderReport(locationName = null) {
        try {
            await this.checkReorderLevels(locationName);
            
            const report = {
                timestamp: new Date().toISOString(),
                location: locationName || 'All Locations',
                total_products_checked: await this.getTotalProductsCount(locationName),
                critical_stock: this.reorderAlerts.filter(item => item.stock_status === 'critical'),
                low_stock: this.reorderAlerts.filter(item => item.stock_status === 'low'),
                total_reorder_value: this.calculateTotalReorderValue(),
                alerts: this.reorderAlerts
            };
            
            return report;
            
        } catch (error) {
            console.error('❌ Error generating reorder report:', error);
            throw error;
        }
    }

    /**
     * Calculate estimated days until stockout based on sales velocity
     */
    async calculateDaysUntilStockout(currentStock, productId) {
        try {
            // Get average daily sales for the last 30 days
            const query = `
                SELECT AVG(daily_quantity) as avg_daily_sales
                FROM (
                    SELECT 
                        DATE(s.sale_date) as sale_date,
                        SUM(si.quantity) as daily_quantity
                    FROM sales_items si
                    JOIN sales s ON si.sale_id = s.sale_id
                    WHERE si.product_id = ?
                    AND s.sale_date >= date('now', '-30 days')
                    GROUP BY DATE(s.sale_date)
                )
            `;
            
            return new Promise((resolve) => {
                this.db.db.get(query, [productId], (err, row) => {
                    if (err || !row || !row.avg_daily_sales || row.avg_daily_sales <= 0) {
                        resolve(null); // Unknown velocity
                    } else {
                        const daysUntilStockout = Math.floor(currentStock / row.avg_daily_sales);
                        resolve(daysUntilStockout);
                    }
                });
            });
            
        } catch (error) {
            console.error('❌ Error calculating stockout days:', error);
            return null;
        }
    }

    /**
     * Get total products count for location
     */
    async getTotalProductsCount(locationName = null) {
        const query = locationName 
            ? `SELECT COUNT(*) as count FROM location_stocks WHERE location = ?`
            : `SELECT COUNT(*) as count FROM products`;
        
        const params = locationName ? [locationName] : [];
        
        return new Promise((resolve) => {
            this.db.db.get(query, params, (err, row) => {
                resolve(err ? 0 : (row?.count || 0));
            });
        });
    }

    /**
     * Calculate total value of items needing reorder
     */
    calculateTotalReorderValue() {
        return this.reorderAlerts.reduce((total, item) => {
            return total + (item.reorder_quantity * (item.price || 0));
        }, 0);
    }

    /**
     * Get stock status for a specific product
     */
    async getProductStockStatus(productId, locationName) {
        const query = `
            SELECT 
                p.description,
                p.min_qty,
                p.max_qty,
                ls.stock,
                ls.location,
                CASE 
                    WHEN ls.stock <= p.min_qty THEN 'critical'
                    WHEN ls.stock <= (p.min_qty + ?) THEN 'low'
                    ELSE 'normal'
                END as stock_status
            FROM products p
            LEFT JOIN location_stocks ls ON p.id = ls.product_id
            WHERE p.id = ? AND ls.location = ?
        `;
        
        return new Promise((resolve, reject) => {
            this.db.db.get(query, [this.lowStockThreshold, productId, locationName], (err, row) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(row);
                }
            });
        });
    }

    /**
     * Update minimum quantity for a product
     */
    async updateMinQuantity(productId, newMinQty) {
        const query = `
            UPDATE products 
            SET min_qty = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        `;
        
        return new Promise((resolve, reject) => {
            this.db.db.run(query, [newMinQty, productId], function(err) {
                if (err) {
                    reject(err);
                } else {
                    console.log(`📦 Updated min quantity for product ${productId} to ${newMinQty}`);
                    resolve({ changes: this.changes });
                }
            });
        });
    }

    /**
     * Add stock to inventory (for receiving new stock)
     */
    async addStock(productId, quantity, locationName, reason = 'stock_received') {
        const query = `
            UPDATE location_stocks 
            SET stock = stock + ?, updated_at = CURRENT_TIMESTAMP
            WHERE product_id = ? AND location = ?
        `;
        
        return new Promise((resolve, reject) => {
            this.db.db.run(query, [quantity, productId, locationName], function(err) {
                if (err) {
                    reject(err);
                } else {
                    console.log(`📦 Added ${quantity} stock for product ${productId} at ${locationName} (${reason})`);
                    resolve({ changes: this.changes });
                }
            });
        });
    }

    /**
     * Get inventory movement history
     */
    async getInventoryMovements(productId = null, locationName = null, days = 30) {
        const query = `
            SELECT 
                si.product_id,
                si.product_name,
                si.quantity,
                s.sale_date,
                s.location_name,
                'sale' as movement_type
            FROM sales_items si
            JOIN sales s ON si.sale_id = s.sale_id
            WHERE s.sale_date >= date('now', '-${days} days')
            ${productId ? 'AND si.product_id = ?' : ''}
            ${locationName ? 'AND s.location_name = ?' : ''}
            ORDER BY s.sale_date DESC
        `;
        
        const params = [];
        if (productId) params.push(productId);
        if (locationName) params.push(locationName);
        
        return new Promise((resolve, reject) => {
            this.db.db.all(query, params, (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }

    /**
     * Set low stock threshold
     */
    setLowStockThreshold(threshold) {
        this.lowStockThreshold = threshold;
        console.log(`📦 Low stock threshold set to ${threshold}`);
    }
}

module.exports = InventoryService;
