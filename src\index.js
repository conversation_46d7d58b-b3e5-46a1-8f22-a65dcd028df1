// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' });

const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs');
const Database = require('./database');
const dayjs = require('dayjs');
const { SupabaseSync } = require('./supabase-config');
const EmailService = require('./services/emailService');
const PrinterService = require('./services/printerService');
const InventoryService = require('./services/inventoryService');

// Handle creating/removing shortcuts on Windows when installing/uninstalling.
if (require('electron-squirrel-startup')) {
  app.quit();
}

let mainWindow;
let db;
let currentUser = null; // Store current logged-in user
let supabaseSync = null; // Supabase sync instance
let emailService = null; // Email service instance
let printerService = null; // Printer service instance
let inventoryService = null; // Inventory service instance

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    center: true, // Center the window on screen
    resizable: true,
    maximizable: true, // Enable maximize functionality
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    },
    titleBarStyle: 'hidden', // Remove default toolbar
    frame: false, // Remove window frame for modern look
    show: false // Don't show window until ready (prevents console opening)
  });

  // Load the welcome page initially
  mainWindow.loadFile(path.join(__dirname, 'welcome.html'));

  // Show window when ready to prevent console opening
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // Initialize database
  console.log('🔧 Initializing database...');
  db = new Database();
  db.init();

  // Add database ready check
  setTimeout(() => {
    if (db && db.db) {
      console.log('✅ Database initialized successfully');

      // Show database location for user reference
      const userDataPath = app.getPath('userData');
      const dbPath = path.join(userDataPath, 'pos_system.db');
      console.log('📁 Database Location:', dbPath);
      console.log('📂 User Data Folder:', userDataPath);
      console.log('💡 To find database: Open Windows Explorer and paste this path:');
      console.log('   ' + userDataPath);
    } else {
      console.error('❌ Database initialization failed');
    }
  }, 1000);

  // Initialize Supabase sync after database is ready
  setTimeout(() => {
    supabaseSync = new SupabaseSync(db);
    console.log('🔄 Supabase sync initialized');

    // Initialize email service
    console.log('📧 Initializing email service...');
    console.log('📧 Environment check:');
    console.log('  - RESEND_API_KEY:', process.env.RESEND_API_KEY ? 'Set' : 'Not set');
    console.log('  - BUSINESS_OWNER_EMAIL:', process.env.BUSINESS_OWNER_EMAIL || 'Not set');
    console.log('  - EMAIL_ENABLED:', process.env.EMAIL_ENABLED || 'Not set');

    emailService = new EmailService();
    console.log('📧 Email service initialized');

    // Initialize printer service
    printerService = new PrinterService();
    printerService.initialize().then(printerInit => {
      console.log('🖨️ Printer service initialized:', printerInit.success ? 'Success' : 'Failed');
    }).catch(error => {
      console.error('❌ Printer service initialization failed:', error);
    });

    // Initialize inventory service
    inventoryService = new InventoryService(db);
    console.log('📦 Inventory service initialized');

    // Detect system printers after window is ready
    setTimeout(() => {
      if (mainWindow && mainWindow.webContents && printerService) {
        mainWindow.webContents.getPrinters().then(systemPrinters => {
          console.log(`🖨️ Found ${systemPrinters.length} system printers`);

          // Add system printers to printer service
          const formattedPrinters = systemPrinters.map(printer => ({
            id: `system_${printer.name.replace(/\s+/g, '_')}`,
            name: printer.name,
            type: 'system',
            connection: 'system',
            status: printer.status === 0 ? 'online' : 'offline',
            isDefault: printer.isDefault,
            description: printer.description || '',
            options: printer.options || {}
          }));

          printerService.systemPrinters = formattedPrinters;
          printerService.availablePrinters = [...printerService.systemPrinters, ...printerService.thermalPrinters];

          // Set default printer if not already set
          if (!printerService.defaultPrinter && formattedPrinters.length > 0) {
            const defaultPrinter = formattedPrinters.find(p => p.isDefault) || formattedPrinters[0];
            printerService.defaultPrinter = defaultPrinter;
          }

          console.log(`✅ Total printers available: ${printerService.availablePrinters.length}`);
        }).catch(error => {
          console.error('❌ Error detecting system printers:', error);
        });
      }
    }, 2000);

    // Test connection and start auto sync
    supabaseSync.testConnection().then(result => {
      if (result.success) {
        console.log('✅ Supabase connection verified, starting auto sync...');
        supabaseSync.startAutoSync();
      } else {
        console.error('❌ Supabase connection failed:', result.error);
      }
    });
  }, 2000); // Wait 2 seconds for database to be fully initialized
}

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// IPC handlers for login
ipcMain.handle('login', async (event, credentials) => {
  try {
    console.log('🔐 Login attempt:', { username: credentials.username, role: credentials.role });

    if (!db || !db.db) {
      console.error('❌ Database not initialized for login');
      return { success: false, message: 'Database not ready. Please wait and try again.' };
    }

    const user = await db.authenticateUser(credentials.username, credentials.password, credentials.role);
    if (user) {
      // Store current user information
      currentUser = user;

      // Load user permissions if not Admin
      let permissions = [];
      if (user.role !== 'Admin') {
        try {
          permissions = await db.getUserPermissions(user.id);
          console.log(`Loaded ${permissions.length} permissions for user ${user.username}`);
        } catch (permError) {
          console.error('Error loading user permissions:', permError);
        }
      }

      // Store permissions with user
      currentUser.permissions = permissions;

      // Automatically maximize window for application interfaces
      if (mainWindow && !mainWindow.isMaximized()) {
        mainWindow.maximize();
      }

      // Determine which interface to load based on user permissions
      const redirectPage = determineUserInterface(permissions, user.role);
      console.log(`Redirecting user ${user.username} (${user.role}) to: ${redirectPage}`);

      // Load the appropriate interface
      const pagePath = redirectPage === 'pos' ? 'pages/pos.html' :
                      redirectPage === 'admin' ? 'pages/admin.html' :
                      'pages/theater.html';

      mainWindow.loadFile(path.join(__dirname, pagePath));
      return { success: true, user: currentUser };
    } else {
      return { success: false, message: 'Invalid credentials' };
    }
  } catch (error) {
    return { success: false, message: 'Database error' };
  }
});

// Determine which interface to load based on user permissions
function determineUserInterface(permissions, userRole) {
  // Admin always goes to POS (can access everything)
  if (userRole === 'Admin') {
    return 'pos';
  }

  // For non-admin users, analyze their permissions
  if (!permissions || permissions.length === 0) {
    console.warn('User has no permissions, defaulting to POS');
    return 'pos';
  }

  const hasPos = permissions.some(p => p.module_id === 'pos');
  const hasTheater = permissions.some(p => p.module_id === 'theater');
  const hasAdmin = permissions.some(p =>
    p.module_id === 'dashboard' ||
    p.module_id === 'master' ||
    p.module_id === 'reports' ||
    p.module_id === 'transactions' ||
    p.module_id === 'wholesale' ||
    p.module_id === 'user-management'
  );

  console.log('Permission analysis:', { hasPos, hasTheater, hasAdmin });

  // Priority logic for redirection
  if (hasTheater && !hasPos && !hasAdmin) {
    // Only theater access - go directly to theater
    return 'theater';
  } else if (hasAdmin && !hasPos && !hasTheater) {
    // Only admin access - go directly to admin
    return 'admin';
  } else if (hasPos) {
    // Has POS access (with or without others) - go to POS
    return 'pos';
  } else {
    // Fallback to POS if unclear
    console.warn('Unclear permissions, defaulting to POS');
    return 'pos';
  }
}

// Get current user information
ipcMain.handle('get-current-user', () => {
  return currentUser;
});

// Refresh current user data (reload from database)
async function refreshCurrentUserData() {
  if (!currentUser || !db) {
    console.log('No current user or database to refresh');
    return false;
  }

  try {
    console.log('🔄 Refreshing current user data from database...');

    // Get fresh user data by ID to include updated location information
    const freshUser = await db.getUserWithLocationById(currentUser.id);

    if (freshUser) {
      // Preserve existing permissions and shift data
      const existingPermissions = currentUser.permissions || [];
      const existingShift = currentUser.current_shift || null;

      // Update current user with fresh data
      currentUser = {
        ...currentUser, // Keep existing data
        ...freshUser,   // Override with fresh data
        permissions: existingPermissions,
        current_shift: existingShift
      };

      console.log('✅ Current user data refreshed successfully');
      console.log('🎬 Updated theater_time:', currentUser.theater_time);
      return true;
    } else {
      console.error('❌ Failed to refresh user data - user not found');
      return false;
    }
  } catch (error) {
    console.error('❌ Error refreshing current user data:', error);
    return false;
  }
}

// IPC handler to refresh current user data
ipcMain.handle('refresh-current-user', async () => {
  try {
    const success = await refreshCurrentUserData();
    return { success, user: currentUser };
  } catch (error) {
    console.error('Error in refresh-current-user handler:', error);
    return { success: false, message: error.message };
  }
});

// Get database location information
ipcMain.handle('get-database-location', () => {
  const userDataPath = app.getPath('userData');
  const dbPath = path.join(userDataPath, 'pos_system.db');

  return {
    userDataPath: userDataPath,
    databasePath: dbPath,
    databaseExists: fs.existsSync(dbPath),
    userDataExists: fs.existsSync(userDataPath)
  };
});

// Refresh entire system - safely reload all data without affecting connections
ipcMain.handle('refresh-entire-system', async () => {
  try {
    console.log('🔄 Starting entire system refresh...');

    // Send refresh signal to renderer process
    if (mainWindow && mainWindow.webContents) {
      mainWindow.webContents.send('system-refresh-requested');
    }

    // Optional: Trigger Supabase sync if available
    if (supabaseSync) {
      try {
        console.log('🔄 Triggering Supabase sync...');
        await supabaseSync.syncAllData();
      } catch (syncError) {
        console.warn('⚠️ Supabase sync warning during refresh:', syncError.message);
        // Don't fail the entire refresh if sync has issues
      }
    }

    console.log('✅ System refresh completed successfully');
    return {
      success: true,
      message: 'System refreshed successfully',
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    console.error('❌ Error during system refresh:', error);
    return {
      success: false,
      message: `Refresh failed: ${error.message}`,
      timestamp: new Date().toISOString()
    };
  }
});

// Location validation helper functions
function validateUserLocation() {
  if (!currentUser) {
    throw new Error('User not authenticated');
  }

  if (!currentUser.location_id) {
    throw new Error('User has no location assigned');
  }

  return true;
}

function isUserAdmin() {
  return currentUser && currentUser.role === 'Admin';
}

function canAccessAllLocations() {
  return isUserAdmin();
}

// Enhanced security logging
function logSecurityEvent(action, details = {}) {
  const timestamp = new Date().toISOString();
  const user = currentUser ? `${currentUser.username} (${currentUser.role})` : 'Unknown';
  const location = currentUser ? `${currentUser.location_name} (ID: ${currentUser.location_id})` : 'No location';

  console.log(`[SECURITY] ${timestamp} - ${action} - User: ${user} - Location: ${location}`, details);
}

// Logout handler
ipcMain.handle('logout', async () => {
  try {
    // Clear current user session
    currentUser = null;

    // Force garbage collection if available
    if (global.gc) {
      global.gc();
    }

    // Reset window to original login state (fixed size, centered)
    if (mainWindow) {
      // Unmaximize if currently maximized
      if (mainWindow.isMaximized()) {
        mainWindow.unmaximize();
      }

      // Reset to original dimensions and center
      mainWindow.setSize(1200, 800);
      mainWindow.center();
    }

    // Navigate to welcome page instead of login page
    await mainWindow.loadFile(path.join(__dirname, 'welcome.html'));

    // Wait for welcome page to load
    await new Promise(resolve => {
      mainWindow.webContents.once('did-finish-load', () => {
        resolve();
      });
    });

    return { success: true };
  } catch (error) {
    console.error('Logout error:', error);
    return { success: false, message: 'Logout failed' };
  }
});

// IPC handlers for navigation
ipcMain.handle('navigate-to', async (event, page) => {
  try {
    switch (page) {
      case 'pos':
        // Automatically maximize window for POS interface
        if (mainWindow && !mainWindow.isMaximized()) {
          mainWindow.maximize();
        }
        mainWindow.loadFile(path.join(__dirname, 'pages', 'pos.html'));
        break;
      case 'admin':
        // Automatically maximize window for admin interface
        if (mainWindow && !mainWindow.isMaximized()) {
          mainWindow.maximize();
        }
        mainWindow.loadFile(path.join(__dirname, 'pages', 'admin.html'));
        break;
      case 'theater':
        // Automatically maximize window for theater interface
        if (mainWindow && !mainWindow.isMaximized()) {
          mainWindow.maximize();
        }
        mainWindow.loadFile(path.join(__dirname, 'pages', 'theater.html'));
        break;
      case 'welcome':
        // Reset window to original state when navigating to welcome
        if (mainWindow) {
          // Unmaximize if currently maximized
          if (mainWindow.isMaximized()) {
            mainWindow.unmaximize();
          }

          // Reset to original dimensions and center
          mainWindow.setSize(1200, 800);
          mainWindow.center();
        }
        mainWindow.loadFile(path.join(__dirname, 'welcome.html'));
        break;
      case 'login':
        // Reset window to original login state when navigating to login
        if (mainWindow) {
          // Unmaximize if currently maximized
          if (mainWindow.isMaximized()) {
            mainWindow.unmaximize();
          }

          // Reset to original dimensions and center
          mainWindow.setSize(1200, 800);
          mainWindow.center();
        }
        mainWindow.loadFile(path.join(__dirname, 'login.html'));
        break;
      case 'database-info':
        // Show database location information
        mainWindow.loadFile(path.join(__dirname, 'database-info.html'));
        break;
      default:
        console.log('Unknown page:', page);
    }
  } catch (error) {
    console.error('Navigation error:', error);
  }
});

// IPC handler for navigating to specific page files
ipcMain.handle('navigate-to-page', async (event, pagePath) => {
  try {
    const fullPath = path.join(__dirname, 'pages', pagePath);
    console.log('Navigating to page:', fullPath);
    mainWindow.loadFile(fullPath);
    return { success: true };
  } catch (error) {
    console.error('Page navigation error:', error);
    return { success: false, message: error.message };
  }
});

// IPC handler for navigating to admin panel sections
ipcMain.handle('navigate-to-admin-section', async (event, section) => {
  try {
    const adminPath = path.join(__dirname, 'pages', 'admin.html');
    console.log('Navigating to admin section:', section);

    // Load admin page first
    await mainWindow.loadFile(adminPath);

    // Wait a bit for the page to load, then trigger the section
    setTimeout(() => {
      mainWindow.webContents.executeJavaScript(`
        if (typeof handleNavClick === 'function') {
          handleNavClick('${section}');
        }
      `);
    }, 500);

    return { success: true };
  } catch (error) {
    console.error('Admin section navigation error:', error);
    return { success: false, message: error.message };
  }
});

// Handle window controls
ipcMain.handle('close-app', () => {
  app.quit();
});

ipcMain.handle('minimize-app', () => {
  if (mainWindow) {
    mainWindow.minimize();
  }
});

ipcMain.handle('toggle-fullscreen', () => {
  if (mainWindow) {
    if (mainWindow.isMaximized()) {
      mainWindow.unmaximize();
      return false;
    } else {
      mainWindow.maximize();
      return true;
    }
  }
  return false;
});

ipcMain.handle('is-fullscreen', () => {
  if (mainWindow) {
    return mainWindow.isMaximized();
  }
  return false;
});

// User Management IPC handlers
ipcMain.handle('create-user', async (event, userData) => {
  try {
    const result = await db.createUser(userData);
    return { success: true, user: result };
  } catch (error) {
    console.error('Error creating user:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-all-users', async () => {
  try {
    const users = await db.getAllUsers();
    return { success: true, users };
  } catch (error) {
    console.error('Error fetching users:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-user-by-id', async (event, id) => {
  try {
    const user = await db.getUserById(id);
    return { success: true, user };
  } catch (error) {
    console.error('Error fetching user:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('update-user', async (event, id, userData) => {
  try {
    const result = await db.updateUser(id, userData);
    return { success: true, result };
  } catch (error) {
    console.error('Error updating user:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('update-user-password', async (event, id, newPassword) => {
  try {
    const result = await db.updateUserPassword(id, newPassword);
    return { success: true, result };
  } catch (error) {
    console.error('Error updating password:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('delete-user', async (event, id) => {
  try {
    const result = await db.deleteUser(id);
    return { success: true, result };
  } catch (error) {
    console.error('Error deleting user:', error);
    return { success: false, message: error.message };
  }
});

// Permission Management IPC handlers
ipcMain.handle('create-user-permissions', async (event, userId, permissions) => {
  try {
    const result = await db.createUserPermissions(userId, permissions);
    return { success: true, result };
  } catch (error) {
    console.error('Error creating user permissions:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-user-permissions', async (event, userId) => {
  try {
    const permissions = await db.getUserPermissions(userId);
    return { success: true, permissions };
  } catch (error) {
    console.error('Error fetching user permissions:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('force-create-permissions-table', async () => {
  try {
    await db.forceCreatePermissionsTable();
    return { success: true };
  } catch (error) {
    console.error('Error force creating permissions table:', error);
    return { success: false, message: error.message };
  }
});

// Debug handler to check database directly
ipcMain.handle('debug-user-permissions', async (event, username) => {
  try {
    // Get user by username
    const user = await db.getUserByUsername(username);
    if (!user) {
      return { success: false, message: 'User not found' };
    }

    // Get permissions
    const permissions = await db.getUserPermissions(user.id);

    return {
      success: true,
      user: user,
      permissions: permissions
    };
  } catch (error) {
    console.error('Error debugging user permissions:', error);
    return { success: false, message: error.message };
  }
});

// Product Management IPC handlers
ipcMain.handle('create-product', async (event, productData, locationStocks) => {
  try {
    const result = await db.createProduct(productData);

    // Create location stocks if provided
    if (locationStocks && locationStocks.length > 0) {
      await db.createLocationStocks(result.id, locationStocks);
    }

    return { success: true, product: result };
  } catch (error) {
    console.error('Error creating product:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-all-products', async () => {
  try {
    console.log('IPC - get-all-products called');

    // Check if user is logged in
    if (!currentUser) {
      console.error('No current user for product access');
      return { success: false, message: 'User not authenticated' };
    }

    let products;
    // Admin users can see all products, others see only their location's products
    if (currentUser.role === 'Admin') {
      products = await db.getAllProducts();
      console.log(`IPC - Admin retrieved ${products.length} products from database`);
    } else {
      if (!currentUser.location_id) {
        console.error('No location assigned for non-admin user');
        return { success: false, message: 'No location assigned' };
      }
      products = await db.getProductsByLocation(currentUser.location_id);
      console.log(`IPC - Retrieved ${products.length} products for location ${currentUser.location_name} (ID: ${currentUser.location_id})`);
    }

    return { success: true, products };
  } catch (error) {
    console.error('IPC - Error fetching products:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-product-by-id', async (event, id) => {
  try {
    const product = await db.getProductById(id);
    let locationStocks = [];

    if (product) {
      locationStocks = await db.getLocationStocksByProductId(id);
    }

    return { success: true, product, locationStocks };
  } catch (error) {
    console.error('Error fetching product:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-product-by-barcode', async (event, barcode) => {
  try {
    const product = await db.getProductByBarcode(barcode);
    let locationStocks = [];

    if (product) {
      locationStocks = await db.getLocationStocksByProductId(product.id);
    }

    return { success: true, product, locationStocks };
  } catch (error) {
    console.error('Error fetching product by barcode:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('update-product', async (event, id, productData, locationStocks) => {
  try {
    const result = await db.updateProduct(id, productData);

    // Update location stocks if provided
    if (locationStocks && locationStocks.length > 0) {
      await db.updateLocationStocks(id, locationStocks);
    }

    return { success: true, result };
  } catch (error) {
    console.error('Error updating product:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('delete-product', async (event, id) => {
  try {
    const result = await db.deleteProduct(id);
    return { success: true, result };
  } catch (error) {
    console.error('Error deleting product:', error);
    return { success: false, message: error.message };
  }
});

// Image upload handler for products
ipcMain.handle('save-product-image', async (event, fileData, barcode) => {
  try {
    // Create images directory in user data path (writable location)
    const userDataPath = app.getPath('userData');
    const imagesDir = path.join(userDataPath, 'images', 'products');
    if (!fs.existsSync(imagesDir)) {
      fs.mkdirSync(imagesDir, { recursive: true });
    }

    // Generate unique filename using barcode and timestamp
    const timestamp = Date.now();
    const fileExtension = path.extname(fileData.name);
    const fileName = `${barcode}_${timestamp}${fileExtension}`;
    const imagePath = path.join(imagesDir, fileName);

    // Convert base64 data to buffer and save
    const base64Data = fileData.data.replace(/^data:image\/[a-z]+;base64,/, '');
    const buffer = Buffer.from(base64Data, 'base64');
    fs.writeFileSync(imagePath, buffer);

    console.log('Image saved successfully:', imagePath);
    return { success: true, imagePath };
  } catch (error) {
    console.error('Error saving product image:', error);
    return { success: false, message: error.message };
  }
});

// Category Management IPC handlers
ipcMain.handle('create-category', async (event, categoryData) => {
  try {
    const result = await db.createCategory(categoryData);
    return { success: true, category: result };
  } catch (error) {
    console.error('Error creating category:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-all-categories', async () => {
  try {
    const categories = await db.getAllCategories();
    return { success: true, categories };
  } catch (error) {
    console.error('Error fetching categories:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-category-by-id', async (event, id) => {
  try {
    const category = await db.getCategoryById(id);
    return { success: true, category };
  } catch (error) {
    console.error('Error fetching category:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-category-by-code', async (event, code) => {
  try {
    const category = await db.getCategoryByCode(code);
    return { success: true, category };
  } catch (error) {
    console.error('Error fetching category by code:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('update-category', async (event, id, categoryData) => {
  try {
    const result = await db.updateCategory(id, categoryData);
    return { success: true, result };
  } catch (error) {
    console.error('Error updating category:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('delete-category', async (event, id) => {
  try {
    const result = await db.deleteCategory(id);
    return { success: true, result };
  } catch (error) {
    console.error('Error deleting category:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-parent-categories', async () => {
  try {
    const categories = await db.getParentCategories();
    return { success: true, categories };
  } catch (error) {
    console.error('Error fetching parent categories:', error);
    return { success: false, message: error.message };
  }
});

// Supplier Management IPC handlers
ipcMain.handle('create-supplier', async (event, supplierData) => {
  try {
    const result = await db.createSupplier(supplierData);
    return { success: true, supplier: result };
  } catch (error) {
    console.error('Error creating supplier:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-all-suppliers', async () => {
  try {
    const suppliers = await db.getAllSuppliers();
    return { success: true, suppliers };
  } catch (error) {
    console.error('Error fetching suppliers:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-supplier-by-id', async (event, id) => {
  try {
    const supplier = await db.getSupplierById(id);
    return { success: true, supplier };
  } catch (error) {
    console.error('Error fetching supplier:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-supplier-by-name', async (event, name) => {
  try {
    const supplier = await db.getSupplierByName(name);
    return { success: true, supplier };
  } catch (error) {
    console.error('Error fetching supplier by name:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('update-supplier', async (event, id, supplierData) => {
  try {
    const result = await db.updateSupplier(id, supplierData);
    return { success: true, result };
  } catch (error) {
    console.error('Error updating supplier:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('delete-supplier', async (event, id) => {
  try {
    const result = await db.deleteSupplier(id);
    return { success: true, result };
  } catch (error) {
    console.error('Error deleting supplier:', error);
    return { success: false, message: error.message };
  }
});

// Location Management IPC handlers
ipcMain.handle('create-location', async (event, locationData) => {
  try {
    const result = await db.createLocation(locationData);
    return { success: true, location: result };
  } catch (error) {
    console.error('Error creating location:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-all-locations', async () => {
  try {
    const locations = await db.getAllLocations();
    return { success: true, locations };
  } catch (error) {
    console.error('Error fetching locations:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-location-by-id', async (event, id) => {
  try {
    const location = await db.getLocationById(id);
    return { success: true, location };
  } catch (error) {
    console.error('Error fetching location:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-location-by-code', async (event, location_code) => {
  try {
    const location = await db.getLocationByCode(location_code);
    return { success: true, location };
  } catch (error) {
    console.error('Error fetching location by code:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('update-location', async (event, id, locationData) => {
  try {
    const result = await db.updateLocation(id, locationData);

    // Check if the updated location affects the current user
    const needsUserRefresh = currentUser && currentUser.location_id &&
                            (currentUser.location_id == id || currentUser.location_id === parseInt(id));

    if (needsUserRefresh) {
      console.log('🔄 Location update affects current user, refreshing user data...');
      await refreshCurrentUserData();
    }

    // Broadcast location update to all windows
    if (mainWindow && mainWindow.webContents) {
      console.log('📡 Broadcasting location update:', id);
      mainWindow.webContents.send('location-updated', {
        locationId: id,
        locationData: locationData,
        timestamp: new Date().toISOString(),
        userRefreshed: needsUserRefresh
      });
    }

    // If the updated location has theater_time, also broadcast a specific theater update
    if (locationData.theater_time !== undefined) {
      console.log('📡 Broadcasting theater time update:', locationData.theater_time);
      mainWindow.webContents.send('theater-time-updated', {
        locationId: id,
        theaterTime: locationData.theater_time,
        timestamp: new Date().toISOString(),
        userRefreshed: needsUserRefresh
      });
    }

    return { success: true, result };
  } catch (error) {
    console.error('Error updating location:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('delete-location', async (event, id) => {
  try {
    const result = await db.deleteLocation(id);
    return { success: true, result };
  } catch (error) {
    console.error('Error deleting location:', error);
    return { success: false, message: error.message };
  }
});

// POS System IPC handlers for categories and products
ipcMain.handle('get-pos-categories', async () => {
  try {
    const categories = await db.getAllCategories();
    // Return only parent categories for main category filter
    const parentCategories = categories.filter(cat => !cat.parent_id && cat.status === 'active');
    return { success: true, categories: parentCategories };
  } catch (error) {
    console.error('Error fetching POS categories:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-pos-subcategories', async (event, parentCategoryId) => {
  try {
    const categories = await db.getAllCategories();
    // Return subcategories for the selected parent category
    const subcategories = categories.filter(cat =>
      cat.parent_id === parentCategoryId && cat.status === 'active'
    );
    return { success: true, subcategories };
  } catch (error) {
    console.error('Error fetching POS subcategories:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-pos-products', async () => {
  try {
    // Validate user location access
    validateUserLocation();
    logSecurityEvent('POS_PRODUCTS_ACCESS', {
      requestType: 'get-pos-products',
      locationId: currentUser.location_id
    });

    // Use location-filtered products
    const products = await db.getProductsByLocation(currentUser.location_id);
    // Get categories and suppliers to populate product details
    const categories = await db.getAllCategories();
    const suppliers = await db.getAllSuppliers();

    // Map products with category and supplier names
    const enrichedProducts = products.map(product => {
      const category = categories.find(c => c.name === product.category);
      const supplier = suppliers.find(s => s.name === product.supplier);

      // Use purchase_price from products table as requested
      const price = parseFloat(product.purchase_price) || 0;

      return {
        id: product.id.toString(),
        name: product.description,
        price: price, // Use purchase_price from products table
        category: product.category,
        subcategory: product.subcategory,
        supplier: product.supplier,
        barcode: product.barcode,
        stock: product.stock || 0,
        daily_item: product.daily_item || 0, // Include daily_item field for deli classification
        image: product.image_path || `https://via.placeholder.com/200x200/4CAF50/FFFFFF?text=${encodeURIComponent(product.description.substring(0, 10))}`
      };
    });

    console.log(`Loaded ${enrichedProducts.length} products for location ${currentUser.location_name} (ID: ${currentUser.location_id})`);

    // Debug pricing for first few products
    if (enrichedProducts.length > 0) {
      console.log('🔍 PRICING DEBUG - First 3 products (using purchase_price from products table):');
      enrichedProducts.slice(0, 3).forEach(product => {
        const originalProduct = products.find(p => p.id.toString() === product.id);
        console.log(`  Product: ${product.name}`);
        console.log(`    Purchase Price (from products table): $${originalProduct?.purchase_price || 'N/A'}`);
        console.log(`    Final Price Used: $${product.price}`);
        console.log(`    Stock: ${product.stock}`);
      });
    }

    return { success: true, products: enrichedProducts };
  } catch (error) {
    console.error('Error fetching POS products:', error);
    return { success: false, message: error.message };
  }
});

// Ticket Management IPC handlers
ipcMain.handle('create-ticket', async (event, ticketData) => {
  try {
    if (!db || !db.db) {
      console.error('Database not initialized');
      return { success: false, message: 'Database not initialized' };
    }

    const result = await db.createTicket(ticketData);
    return { success: true, ticket: result };
  } catch (error) {
    console.error('Error creating ticket:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-all-tickets', async () => {
  try {
    if (!db || !db.db) {
      console.error('Database not initialized for get-all-tickets');
      return { success: false, message: 'Database not initialized' };
    }

    // Validate user location access
    validateUserLocation();
    logSecurityEvent('TICKETS_ACCESS', {
      requestType: 'get-all-tickets',
      locationId: currentUser.location_id
    });

    // Use location-filtered tickets
    const tickets = await db.getTicketsByLocation(currentUser.location_id);
    console.log(`Loaded ${tickets.length} tickets for location ${currentUser.location_name} (ID: ${currentUser.location_id})`);
    return { success: true, tickets };
  } catch (error) {
    console.error('Error fetching tickets:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-ticket-by-id', async (event, ticketId) => {
  try {
    const ticket = await db.getTicketById(ticketId);
    return { success: true, ticket };
  } catch (error) {
    console.error('Error fetching ticket:', error);
    return { success: false, message: error.message };
  }
});

// Save ticket photo to file system
ipcMain.handle('save-ticket-photo', async (event, photoData, ticketId) => {
  try {
    if (!photoData) {
      return { success: true, photoPath: null };
    }

    // Create tickets directory in user data path (writable location)
    const userDataPath = app.getPath('userData');
    const ticketsDir = path.join(userDataPath, 'images', 'tickets');
    if (!fs.existsSync(ticketsDir)) {
      fs.mkdirSync(ticketsDir, { recursive: true });
    }

    // Generate filename with local timestamp using Day.js
    const timestamp = dayjs().format('YYYY-MM-DDTHH-mm-ss-SSS');
    const filename = `ticket_${ticketId}_${timestamp}.jpg`;
    const filePath = path.join(ticketsDir, filename);

    // Convert base64 to buffer and save
    const base64Data = photoData.replace(/^data:image\/[a-z]+;base64,/, '');
    const buffer = Buffer.from(base64Data, 'base64');

    fs.writeFileSync(filePath, buffer);

    // Return relative path for database storage
    const relativePath = path.join('images', 'tickets', filename);

    console.log('Ticket photo saved:', relativePath);
    return { success: true, photoPath: relativePath };
  } catch (error) {
    console.error('Error saving ticket photo:', error);
    return { success: false, message: error.message };
  }
});

// Get full path for ticket photo
ipcMain.handle('get-ticket-photo-path', async (event, relativePath) => {
  try {
    if (!relativePath) {
      return { success: false, message: 'No photo path provided' };
    }

    // Convert relative path to absolute path in user data directory
    const userDataPath = app.getPath('userData');
    const fullPath = path.join(userDataPath, relativePath);

    // Check if file exists
    if (fs.existsSync(fullPath)) {
      return { success: true, fullPath };
    } else {
      console.warn('Ticket photo not found:', fullPath);
      return { success: false, message: 'Photo file not found' };
    }
  } catch (error) {
    console.error('Error getting ticket photo path:', error);
    return { success: false, message: error.message };
  }
});

// Banned Ticket Management IPC handlers
ipcMain.handle('ban-ticket', async (event, ticketId, banData) => {
  try {
    if (!db || !db.db) {
      console.error('Database not initialized');
      return { success: false, message: 'Database not initialized' };
    }

    const result = await db.banTicket(ticketId, banData);
    return { success: true, data: result };
  } catch (error) {
    console.error('Error banning ticket:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-banned-tickets', async (event) => {
  try {
    if (!db || !db.db) {
      console.error('Database not initialized');
      return { success: false, message: 'Database not initialized' };
    }

    // Check if user is logged in and has location
    if (!currentUser || !currentUser.location_id) {
      console.error('No current user or location for banned ticket access');
      return { success: false, message: 'User not authenticated or no location assigned' };
    }

    // Use location-filtered banned tickets
    const bannedTickets = await db.getBannedTicketsByLocation(currentUser.location_id);
    console.log(`Loaded ${bannedTickets.length} banned tickets for location ${currentUser.location_name} (ID: ${currentUser.location_id})`);
    return { success: true, data: bannedTickets };
  } catch (error) {
    console.error('Error getting banned tickets:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('refund-banned-ticket', async (event, bannedTicketId, refundData) => {
  try {
    if (!db || !db.db) {
      console.error('Database not initialized');
      return { success: false, message: 'Database not initialized' };
    }

    const result = await db.refundBannedTicket(bannedTicketId, refundData);
    return { success: true, data: result };
  } catch (error) {
    console.error('Error refunding banned ticket:', error);
    return { success: false, message: error.message };
  }
});

// Draft Sales Management IPC Handlers
ipcMain.handle('create-draft-sale', async (event, draftData) => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!db || !db.db) {
      console.error('Database not initialized');
      return { success: false, message: 'Database not initialized' };
    }

    // Generate unique draft sale ID
    const draftSaleId = `DRAFT-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    // Add current user and location info to draft data
    const completeDraftData = {
      ...draftData,
      draftSaleId,
      userId: currentUser.id,
      locationId: currentUser.location_id,
      operatorName: currentUser.name || currentUser.username,
      locationName: currentUser.location_name
    };

    console.log(`Creating draft sale ${draftSaleId} for location ${currentUser.location_name} by ${currentUser.username}`);

    const result = await db.createDraftSale(completeDraftData);
    console.log(`✅ SECURITY: Draft sale ${draftSaleId} saved to location ${currentUser.location_name} only`);

    return { success: true, data: result };
  } catch (error) {
    console.error('Error creating draft sale:', error);
    return { success: false, message: error.message };
  }
});

// Get draft sales by location (location-based access control)
ipcMain.handle('get-draft-sales', async () => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!db || !db.db) {
      console.error('Database not initialized');
      return { success: false, message: 'Database not initialized' };
    }

    let drafts;
    if (currentUser.role === 'Admin') {
      // Admin can see all draft sales
      drafts = await db.getAllDraftSales();
      console.log(`Admin retrieved ${drafts.length} draft sales from all locations`);
    } else {
      // Non-admin users can only see draft sales from their location
      if (!currentUser.location_id) {
        console.error('No location assigned for non-admin user');
        return { success: false, message: 'No location assigned to user' };
      }

      drafts = await db.getDraftSalesByLocation(currentUser.location_id);
      console.log(`✅ SECURITY: User retrieved ${drafts.length} draft sales from location ${currentUser.location_name} only`);
    }

    return { success: true, drafts };
  } catch (error) {
    console.error('Error fetching draft sales:', error);
    return { success: false, message: error.message };
  }
});

// Get draft sale details
ipcMain.handle('get-draft-sale-details', async (event, draftSaleId) => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!db || !db.db) {
      console.error('Database not initialized');
      return { success: false, message: 'Database not initialized' };
    }

    const draftDetails = await db.getDraftSaleDetails(draftSaleId);

    if (!draftDetails) {
      return { success: false, message: 'Draft sale not found' };
    }

    // Security check: Non-admin users can only access draft sales from their location
    if (currentUser.role !== 'Admin' && draftDetails.location_id !== currentUser.location_id) {
      console.log(`✅ SECURITY: Access denied - User ${currentUser.username} tried to access draft sale from different location`);
      return { success: false, message: 'Access denied: Draft sale not from your location' };
    }

    return { success: true, draft: draftDetails };
  } catch (error) {
    console.error('Error fetching draft sale details:', error);
    return { success: false, message: error.message };
  }
});

// Delete draft sale
ipcMain.handle('delete-draft-sale', async (event, draftSaleId) => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!db || !db.db) {
      console.error('Database not initialized');
      return { success: false, message: 'Database not initialized' };
    }

    // First get draft details to check location access
    const draftDetails = await db.getDraftSaleDetails(draftSaleId);

    if (!draftDetails) {
      return { success: false, message: 'Draft sale not found' };
    }

    // Security check: Non-admin users can only delete draft sales from their location
    if (currentUser.role !== 'Admin' && draftDetails.location_id !== currentUser.location_id) {
      console.log(`✅ SECURITY: Delete denied - User ${currentUser.username} tried to delete draft sale from different location`);
      return { success: false, message: 'Access denied: Cannot delete draft sale from different location' };
    }

    const result = await db.deleteDraftSale(draftSaleId);
    console.log(`✅ SECURITY: Draft sale ${draftSaleId} deleted by ${currentUser.username} from location ${currentUser.location_name}`);

    return { success: true, data: result };
  } catch (error) {
    console.error('Error deleting draft sale:', error);
    return { success: false, message: error.message };
  }
});

// Sales Management IPC Handlers
ipcMain.handle('create-sale', async (event, saleData) => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!db || !db.db) {
      console.error('Database not initialized');
      return { success: false, message: 'Database not initialized' };
    }

    // Generate unique sale ID
    const saleId = `SALE-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    // Add current user and location info to sale data
    const completeSaleData = {
      ...saleData,
      saleId,
      userId: currentUser.id,
      locationId: currentUser.location_id,
      operatorName: currentUser.name || currentUser.username,
      locationName: currentUser.location_name
    };

    console.log(`Creating sale ${saleId} for location ${currentUser.location_name} by ${currentUser.username}`);

    const result = await db.createSale(completeSaleData);
    console.log(`✅ SECURITY: Sale ${saleId} saved to location ${currentUser.location_name} only`);

    // Update inventory after successful sale
    if (inventoryService && saleData.items && saleData.items.length > 0) {
      try {
        console.log(`📦 Updating inventory for ${saleData.items.length} items at ${currentUser.location_name}`);
        console.log('📦 Sale items:', saleData.items.map(item => ({
          productId: item.productId,
          name: item.name,
          quantity: item.quantity
        })));

        const inventoryResult = await inventoryService.updateInventoryAfterSale(saleData.items, currentUser.location_name);

        if (inventoryResult.success) {
          console.log('✅ Inventory updated successfully after sale');
        } else {
          console.error('❌ Inventory update failed:', inventoryResult.error);
        }
      } catch (inventoryError) {
        console.error('❌ Error updating inventory:', inventoryError);
        // Don't fail the sale if inventory update fails
      }
    } else {
      console.log('⚠️ Inventory service not available or no items to update');
    }

    return { success: true, data: result };
  } catch (error) {
    console.error('Error creating sale:', error);
    return { success: false, message: error.message };
  }
});

// Inventory Management IPC Handlers
ipcMain.handle('get-reorder-alerts', async () => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!inventoryService) {
      return { success: false, message: 'Inventory service not initialized' };
    }

    // Get reorder alerts for current user's location (or all locations for admin)
    const locationName = currentUser.role === 'Admin' ? null : currentUser.location_name;
    const report = await inventoryService.getReorderReport(locationName);

    // Simplify the report to avoid cloning issues
    const simplifiedReport = {
      timestamp: report.timestamp,
      location: report.location,
      total_products_checked: report.total_products_checked,
      critical_stock_count: report.critical_stock.length,
      low_stock_count: report.low_stock.length,
      total_reorder_value: report.total_reorder_value,
      alerts: report.alerts.map(alert => ({
        id: alert.id,
        barcode: alert.barcode,
        description: alert.description,
        category: alert.category,
        subcategory: alert.subcategory,
        supplier: alert.supplier,
        location: alert.location,
        stock: alert.stock,
        min_qty: alert.min_qty,
        max_qty: alert.max_qty,
        price: alert.price,
        stock_status: alert.stock_status,
        reorder_quantity: alert.reorder_quantity,
        days_until_stockout: alert.days_until_stockout,
        last_checked: alert.last_checked
      }))
    };

    return { success: true, data: simplifiedReport };
  } catch (error) {
    console.error('Error getting reorder alerts:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('check-reorder-levels', async () => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!inventoryService) {
      return { success: false, message: 'Inventory service not initialized' };
    }

    const locationName = currentUser.role === 'Admin' ? null : currentUser.location_name;
    const alerts = await inventoryService.checkReorderLevels(locationName);

    return { success: true, data: alerts };
  } catch (error) {
    console.error('Error checking reorder levels:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-product-stock-status', async (event, productId) => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!inventoryService) {
      return { success: false, message: 'Inventory service not initialized' };
    }

    const status = await inventoryService.getProductStockStatus(productId, currentUser.location_name);
    return { success: true, data: status };
  } catch (error) {
    console.error('Error getting product stock status:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('update-min-quantity', async (event, productId, newMinQty) => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    if (currentUser.role !== 'Admin') {
      return { success: false, message: 'Admin access required' };
    }

    if (!inventoryService) {
      return { success: false, message: 'Inventory service not initialized' };
    }

    const result = await inventoryService.updateMinQuantity(productId, newMinQty);
    return { success: true, data: result };
  } catch (error) {
    console.error('Error updating min quantity:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('add-stock', async (event, productId, quantity, reason = 'manual_adjustment') => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!inventoryService) {
      return { success: false, message: 'Inventory service not initialized' };
    }

    const result = await inventoryService.addStock(productId, quantity, currentUser.location_name, reason);
    return { success: true, data: result };
  } catch (error) {
    console.error('Error adding stock:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-inventory-movements', async (event, productId = null, days = 30) => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!inventoryService) {
      return { success: false, message: 'Inventory service not initialized' };
    }

    const locationName = currentUser.role === 'Admin' ? null : currentUser.location_name;
    const movements = await inventoryService.getInventoryMovements(productId, locationName, days);

    return { success: true, data: movements };
  } catch (error) {
    console.error('Error getting inventory movements:', error);
    return { success: false, message: error.message };
  }
});

// Debug inventory system
ipcMain.handle('debug-inventory', async () => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!inventoryService) {
      return { success: false, message: 'Inventory service not initialized' };
    }

    console.log('🔍 DEBUG: Checking inventory system...');

    // Get all products with their stock info
    const products = await db.getAllProducts();
    const debugInfo = [];

    for (const product of products.slice(0, 5)) { // Check first 5 products
      const locationStocks = await db.getLocationStocksByProductId(product.id);
      debugInfo.push({
        id: product.id,
        name: product.description,
        barcode: product.barcode,
        min_qty: product.min_qty,
        max_qty: product.max_qty,
        location_stocks: locationStocks
      });
    }

    // Get reorder alerts
    const reorderReport = await inventoryService.getReorderReport(currentUser.location_name);

    console.log('🔍 DEBUG Results:', {
      products_checked: debugInfo.length,
      reorder_alerts: reorderReport.alerts.length,
      critical_stock: reorderReport.critical_stock.length,
      low_stock: reorderReport.low_stock.length
    });

    // Simplify data to avoid cloning issues
    const simplifiedData = {
      products: debugInfo.map(p => ({
        id: p.id,
        name: p.name,
        barcode: p.barcode,
        min_qty: p.min_qty,
        max_qty: p.max_qty,
        location_stocks: p.location_stocks.map(ls => ({
          location: ls.location,
          stock: ls.stock,
          price: ls.price
        }))
      })),
      reorder_report: {
        total_products_checked: reorderReport.total_products_checked,
        critical_stock_count: reorderReport.critical_stock.length,
        low_stock_count: reorderReport.low_stock.length,
        total_reorder_value: reorderReport.total_reorder_value,
        alerts: reorderReport.alerts.map(alert => ({
          id: alert.id,
          description: alert.description,
          barcode: alert.barcode,
          location: alert.location,
          stock: alert.stock,
          min_qty: alert.min_qty,
          max_qty: alert.max_qty,
          stock_status: alert.stock_status,
          reorder_quantity: alert.reorder_quantity
        }))
      },
      user_location: currentUser.location_name
    };

    return {
      success: true,
      data: simplifiedData
    };
  } catch (error) {
    console.error('❌ Error debugging inventory:', error);
    return { success: false, message: error.message };
  }
});

// Update minimum quantity for a product
ipcMain.handle('update-min-quantity', async (event, productId, newMinQty) => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    console.log(`📦 Updating min quantity for product ${productId} to ${newMinQty}`);

    const updateQuery = `
      UPDATE products
      SET min_qty = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;

    return new Promise((resolve) => {
      db.db.run(updateQuery, [newMinQty, productId], function(err) {
        if (err) {
          console.error('❌ Error updating min quantity:', err);
          resolve({ success: false, message: err.message });
        } else {
          console.log('✅ Min quantity updated successfully');
          resolve({ success: true, changes: this.changes });
        }
      });
    });
  } catch (error) {
    console.error('❌ Error updating min quantity:', error);
    return { success: false, message: error.message };
  }
});

// Add stock to a product (increase max_qty)
ipcMain.handle('add-stock', async (event, productId, quantity, reason = 'manual_restock') => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    console.log(`📦 Adding ${quantity} stock to product ${productId} (${reason})`);

    const updateQuery = `
      UPDATE products
      SET max_qty = max_qty + ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;

    return new Promise((resolve) => {
      db.db.run(updateQuery, [quantity, productId], function(err) {
        if (err) {
          console.error('❌ Error adding stock:', err);
          resolve({ success: false, message: err.message });
        } else {
          console.log('✅ Stock added successfully');
          resolve({ success: true, changes: this.changes });
        }
      });
    });
  } catch (error) {
    console.error('❌ Error adding stock:', error);
    return { success: false, message: error.message };
  }
});

// Get sales by location (location-based access control)
ipcMain.handle('get-sales', async () => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!db || !db.db) {
      console.error('Database not initialized');
      return { success: false, message: 'Database not initialized' };
    }

    let sales;
    if (currentUser.role === 'Admin') {
      // Admin can see all sales
      sales = await db.getAllSales();
      console.log(`Admin retrieved ${sales.length} sales from all locations`);
    } else {
      // Non-admin users can only see sales from their location
      if (!currentUser.location_id) {
        console.error('No location assigned for non-admin user');
        return { success: false, message: 'No location assigned to user' };
      }

      sales = await db.getSalesByLocation(currentUser.location_id);
      console.log(`✅ SECURITY: User retrieved ${sales.length} sales from location ${currentUser.location_name} only`);
    }

    return { success: true, sales };
  } catch (error) {
    console.error('Error fetching sales:', error);
    return { success: false, message: error.message };
  }
});

// Daily Sales Report IPC Handlers
ipcMain.handle('get-daily-sales-report', async (event, { dateFrom, dateTo, shift, saleType, operator }) => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!db || !db.db) {
      console.error('Database not initialized');
      return { success: false, message: 'Database not initialized' };
    }

    // Validate user location access
    validateUserLocation();

    const locationId = currentUser.location_id;
    let reportData = [];

    // Ensure operator defaults to 'all' if not provided
    const operatorFilter = operator || 'all';

    console.log(`Generating daily sales report for location ${currentUser.location_name} (${locationId})`);
    console.log(`Date range: ${dateFrom} to ${dateTo}, Shift: ${shift}, Type: ${saleType}, Operator: ${operatorFilter}`);

    // Get data based on sale type filter
    if (saleType === 'sale' || saleType === 'all') {
      const salesData = await db.getDailySalesReport(locationId, dateFrom, dateTo, shift, operatorFilter);
      reportData = reportData.concat(salesData);
    }

    if (saleType === 'theater' || saleType === 'all') {
      const theaterData = await db.getTheaterSalesReport(locationId, dateFrom, dateTo, shift, operatorFilter);
      reportData = reportData.concat(theaterData);
    }

    if (saleType === 'deli' || saleType === 'all') {
      const deliData = await db.getDeliSalesReport(locationId, dateFrom, dateTo, shift, operatorFilter);
      reportData = reportData.concat(deliData);
    }

    console.log(`✅ SECURITY: Daily sales report generated for location ${currentUser.location_name} only`);
    console.log(`Report contains ${reportData.length} records`);

    return {
      success: true,
      reportData,
      location: currentUser.location_name,
      operator: currentUser.name || currentUser.username
    };
  } catch (error) {
    console.error('Error generating daily sales report:', error);
    return { success: false, message: error.message };
  }
});

// Get operators for daily sales report
ipcMain.handle('get-daily-sales-operators', async () => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!db || !db.db) {
      return { success: false, message: 'Database not initialized' };
    }

    // Validate user location access
    validateUserLocation();

    const operators = await db.getDailySalesOperators(currentUser.location_id);

    console.log(`✅ SECURITY: Operators loaded for location ${currentUser.location_name} only`);
    console.log(`Found ${operators.length} operators with sales data`);

    return {
      success: true,
      operators
    };
  } catch (error) {
    console.error('Error getting daily sales operators:', error);
    return { success: false, message: error.message };
  }
});

// Get sale details
ipcMain.handle('get-sale-details', async (event, saleId) => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!db || !db.db) {
      console.error('Database not initialized');
      return { success: false, message: 'Database not initialized' };
    }

    const saleDetails = await db.getSaleDetails(saleId);

    if (!saleDetails) {
      return { success: false, message: 'Sale not found' };
    }

    // Security check: Non-admin users can only access sales from their location
    if (currentUser.role !== 'Admin' && saleDetails.location_id !== currentUser.location_id) {
      console.log(`✅ SECURITY: Access denied - User ${currentUser.username} tried to access sale from different location`);
      return { success: false, message: 'Access denied: Sale not from your location' };
    }

    return { success: true, sale: saleDetails };
  } catch (error) {
    console.error('Error fetching sale details:', error);
    return { success: false, message: error.message };
  }
});

// Shift Management IPC Handlers
ipcMain.handle('start-shift', async (event, shiftData) => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!db || !db.db) {
      console.error('Database not initialized');
      return { success: false, message: 'Database not initialized' };
    }

    // Generate unique shift ID
    const shiftId = `SHIFT-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    // Add current user and location info to shift data
    const completeShiftData = {
      ...shiftData,
      shift_id: shiftId,
      user_id: currentUser.id,
      location_id: currentUser.location_id,
      operator_name: currentUser.name || currentUser.username,
      location_name: currentUser.location_name
    };



    const result = await db.startShift(completeShiftData);

    // Store current shift in memory for quick access
    currentUser.current_shift = result;

    return { success: true, shift: result };
  } catch (error) {
    console.error('Error starting shift:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-current-shift', async () => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!db || !db.db) {
      console.error('Database not initialized');
      return { success: false, message: 'Database not initialized' };
    }

    const shift = await db.getCurrentShift(currentUser.id, currentUser.location_id);

    if (shift) {
      // Update current shift in memory
      currentUser.current_shift = shift;

      // Calculate remaining time using day.js (same as tickets)
      const now = dayjs();
      const endTime = dayjs(shift.shift_end_time);

      if (!endTime.isValid()) {
        return { success: false, message: 'Invalid shift end time' };
      }

      const remainingMs = endTime.diff(now);
      const remainingMinutes = Math.max(0, Math.floor(remainingMs / (1000 * 60)));

      // Update remaining time in database if it's different
      if (shift.remaining_time_minutes !== remainingMinutes) {
        await db.updateShiftRemainingTime(shift.shift_id, remainingMinutes);
      }

      return {
        success: true,
        shift: {
          ...shift,
          remaining_minutes: remainingMinutes,
          is_near_end: remainingMinutes <= 30 && remainingMinutes > 0,
          is_expired: remainingMinutes <= 0
        }
      };
    } else {
      return { success: true, shift: null };
    }
  } catch (error) {
    console.error('Error getting current shift:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('end-shift', async (event, shiftSummary) => {
  try {
    if (!currentUser || !currentUser.current_shift) {
      return { success: false, message: 'No active shift found' };
    }

    if (!db || !db.db) {
      console.error('Database not initialized');
      return { success: false, message: 'Database not initialized' };
    }

    const shiftId = currentUser.current_shift.shift_id;

    const result = await db.endShift(shiftId, shiftSummary);

    if (result) {
      // Clear current shift from memory
      currentUser.current_shift = null;
      return { success: true };
    } else {
      return { success: false, message: 'Shift not found or already ended' };
    }
  } catch (error) {
    console.error('Error ending shift:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('update-shift-time', async (event, remainingMinutes) => {
  try {
    if (!currentUser || !currentUser.current_shift) {
      return { success: false, message: 'No active shift found' };
    }

    if (!db || !db.db) {
      return { success: false, message: 'Database not initialized' };
    }

    const shiftId = currentUser.current_shift.shift_id;
    const result = await db.updateShiftRemainingTime(shiftId, remainingMinutes);

    return { success: result };
  } catch (error) {
    console.error('Error updating shift time:', error);
    return { success: false, message: error.message };
  }
});

// Get shift totals
ipcMain.handle('get-shift-totals', async (event, shiftId) => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!db || !db.db) {
      return { success: false, message: 'Database not initialized' };
    }

    // Recalculate totals from sales history to ensure accuracy
    const totals = await db.recalculateShiftTotals(shiftId);
    return { success: true, data: totals };
  } catch (error) {
    console.error('Error getting shift totals:', error);
    return { success: false, message: error.message };
  }
});

// Recalculate all shift totals (admin utility)
ipcMain.handle('recalculate-all-shift-totals', async () => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!db || !db.db) {
      return { success: false, message: 'Database not initialized' };
    }

    console.log('🔄 Starting recalculation of all shift totals...');
    const results = await db.recalculateAllShiftTotals();
    console.log(`✅ Recalculated totals for ${results.length} shifts`);

    return {
      success: true,
      message: `Successfully recalculated totals for ${results.length} shifts`,
      data: results
    };
  } catch (error) {
    console.error('Error recalculating all shift totals:', error);
    return { success: false, message: error.message };
  }
});

// Debug: Get user sales history
ipcMain.handle('debug-user-sales', async (event, userId) => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!db || !db.db) {
      return { success: false, message: 'Database not initialized' };
    }

    const query = `
      SELECT s.*,
             COUNT(si.id) as item_count,
             datetime(s.sale_date, 'localtime') as local_sale_date
      FROM sales s
      LEFT JOIN sales_items si ON s.sale_id = si.sale_id
      WHERE s.user_id = ?
      GROUP BY s.id
      ORDER BY s.sale_date DESC
      LIMIT 20
    `;

    const sales = await new Promise((resolve, reject) => {
      db.db.all(query, [userId], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    return { success: true, data: sales };
  } catch (error) {
    console.error('Error getting user sales:', error);
    return { success: false, message: error.message };
  }
});

// Debug: Get user shift history
ipcMain.handle('debug-user-shifts', async (event, userId) => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!db || !db.db) {
      return { success: false, message: 'Database not initialized' };
    }

    const query = `
      SELECT *,
             datetime(shift_start_time, 'localtime') as local_start_time,
             datetime(shift_end_time, 'localtime') as local_end_time,
             datetime(actual_end_time, 'localtime') as local_actual_end_time
      FROM shifts
      WHERE user_id = ?
      ORDER BY shift_start_time DESC
      LIMIT 10
    `;

    const shifts = await new Promise((resolve, reject) => {
      db.db.all(query, [userId], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    return { success: true, data: shifts };
  } catch (error) {
    console.error('Error getting user shifts:', error);
    return { success: false, message: error.message };
  }
});

// Debug: Check current active shift for user
ipcMain.handle('debug-current-shift', async (event, userId, locationId) => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!db || !db.db) {
      return { success: false, message: 'Database not initialized' };
    }

    console.log(`🔍 DEBUG: Checking current shift for user ${userId}, location ${locationId}`);

    const shift = await db.getCurrentShift(userId, locationId);

    if (shift) {
      console.log(`✅ DEBUG: Found active shift:`, shift);
      return { success: true, data: shift, hasActiveShift: true };
    } else {
      console.log(`❌ DEBUG: No active shift found for user ${userId}, location ${locationId}`);
      return { success: true, data: null, hasActiveShift: false };
    }
  } catch (error) {
    console.error('Error checking current shift:', error);
    return { success: false, message: error.message };
  }
});

// Get current shift report
ipcMain.handle('get-current-shift-report', async () => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!db || !db.db) {
      return { success: false, message: 'Database not initialized' };
    }

    console.log(`📊 Getting current shift report for user ${currentUser.username}`);

    // Get current active shift
    const shift = await db.getCurrentShift(currentUser.id, currentUser.location_id);

    if (!shift) {
      return {
        success: true,
        data: { shift: null, sales: [] },
        message: 'No active shift found'
      };
    }

    // Get sales for this shift (both regular sales and theater tickets)
    const salesQuery = `
      SELECT s.*,
             datetime(s.sale_date, 'localtime') as local_sale_date,
             'sale' as record_type
      FROM sales s
      WHERE s.user_id = ?
        AND s.location_id = ?
        AND s.sale_date >= ?
        AND s.sale_date <= COALESCE(?, datetime('now', 'localtime'))
        AND s.status = 'completed'
      ORDER BY s.sale_date DESC
    `;

    const ticketsQuery = `
      SELECT t.*,
             datetime(t.issued_at, 'localtime') as local_sale_date,
             'ticket' as record_type,
             t.total_amount,
             t.payment_method as payment_type
      FROM tickets t
      WHERE t.user_id = ?
        AND t.location_id = ?
        AND t.issued_at >= ?
        AND t.issued_at <= COALESCE(?, datetime('now', 'localtime'))
        AND t.status = 'active'
      ORDER BY t.issued_at DESC
    `;

    const [sales, tickets] = await Promise.all([
      new Promise((resolve, reject) => {
        db.db.all(salesQuery, [
          currentUser.id,
          currentUser.location_id,
          shift.shift_start_time,
          shift.actual_end_time || shift.shift_end_time
        ], (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        });
      }),
      new Promise((resolve, reject) => {
        db.db.all(ticketsQuery, [
          currentUser.id,
          currentUser.location_id,
          shift.shift_start_time,
          shift.actual_end_time || shift.shift_end_time
        ], (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        });
      })
    ]);

    // Combine sales and tickets into one array
    const allTransactions = [...sales, ...tickets];

    console.log(`✅ SECURITY: Shift report generated for user ${currentUser.username} at location ${currentUser.location_name}`);
    console.log(`Report contains: Shift ${shift.shift_id}, ${sales.length} sales, ${tickets.length} tickets`);

    return {
      success: true,
      data: {
        shift: shift,
        sales: sales,
        tickets: tickets,
        allTransactions: allTransactions
      }
    };
  } catch (error) {
    console.error('Error getting current shift report:', error);
    return { success: false, message: error.message };
  }
});

// Get shift filter options (operators and shifts for current location)
ipcMain.handle('get-shift-filter-options', async () => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!db || !db.db) {
      return { success: false, message: 'Database not initialized' };
    }

    console.log(`🔍 Getting shift filter options for location ${currentUser.location_id}`);

    // Get operators who have worked shifts at this location
    const operatorsQuery = `
      SELECT DISTINCT u.id as user_id, u.username, COUNT(s.shift_id) as shift_count
      FROM users u
      INNER JOIN shifts s ON u.id = s.user_id
      WHERE s.location_id = ?
      GROUP BY u.id, u.username
      ORDER BY u.username
    `;

    const operators = await new Promise((resolve, reject) => {
      db.db.all(operatorsQuery, [currentUser.location_id], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    // Get recent shifts for this location (last 30 days)
    const shiftsQuery = `
      SELECT s.shift_id, s.user_id, s.shift_start_time, s.shift_end_time, s.actual_end_time, s.status,
             u.username as operator_name
      FROM shifts s
      INNER JOIN users u ON s.user_id = u.id
      WHERE s.location_id = ?
        AND s.shift_start_time >= datetime('now', '-30 days', 'localtime')
      ORDER BY s.shift_start_time DESC
      LIMIT 50
    `;

    const shifts = await new Promise((resolve, reject) => {
      db.db.all(shiftsQuery, [currentUser.location_id], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log(`✅ SECURITY: Filter options loaded for location ${currentUser.location_name}`);
    console.log(`Found ${operators.length} operators, ${shifts.length} recent shifts`);

    return {
      success: true,
      data: {
        operators: operators,
        shifts: shifts,
        currentUserId: currentUser.id
      }
    };
  } catch (error) {
    console.error('Error getting shift filter options:', error);
    return { success: false, message: error.message };
  }
});

// Get filtered shift report
ipcMain.handle('get-filtered-shift-report', async (event, filters) => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!db || !db.db) {
      return { success: false, message: 'Database not initialized' };
    }

    const { operatorFilter, periodFilter } = filters;

    console.log(`📊 Getting filtered shift report: operator=${operatorFilter}, period=${periodFilter}`);

    let shift;
    let targetUserId = currentUser.id;
    let targetLocationId = currentUser.location_id;

    // Determine which shift to get
    if (periodFilter === 'current') {
      // Get current active shift for specified operator
      if (operatorFilter !== 'current') {
        targetUserId = parseInt(operatorFilter);
      }
      shift = await db.getCurrentShift(targetUserId, targetLocationId);
    } else {
      // Get specific shift by ID
      const shiftQuery = `
        SELECT s.*, u.username as operator_name, l.location as location_name
        FROM shifts s
        INNER JOIN users u ON s.user_id = u.id
        INNER JOIN locations l ON s.location_id = l.id
        WHERE s.shift_id = ? AND s.location_id = ?
      `;

      shift = await new Promise((resolve, reject) => {
        db.db.get(shiftQuery, [periodFilter, currentUser.location_id], (err, row) => {
          if (err) reject(err);
          else resolve(row);
        });
      });

      if (shift) {
        targetUserId = shift.user_id;
      }
    }

    if (!shift) {
      return {
        success: true,
        data: { shift: null, sales: [] },
        message: 'No shift found matching the selected filters'
      };
    }

    // Security check: Only allow viewing shifts from current location
    if (shift.location_id !== currentUser.location_id) {
      return { success: false, message: 'Access denied: Cannot view shifts from other locations' };
    }

    // Get sales for this shift (both regular sales and theater tickets)
    const salesQuery = `
      SELECT s.*,
             datetime(s.sale_date, 'localtime') as local_sale_date,
             'sale' as record_type
      FROM sales s
      WHERE s.user_id = ?
        AND s.location_id = ?
        AND s.sale_date >= ?
        AND s.sale_date <= COALESCE(?, datetime('now', 'localtime'))
        AND s.status = 'completed'
      ORDER BY s.sale_date DESC
    `;

    const ticketsQuery = `
      SELECT t.*,
             datetime(t.issued_at, 'localtime') as local_sale_date,
             'ticket' as record_type,
             t.total_amount,
             t.payment_method as payment_type
      FROM tickets t
      WHERE t.user_id = ?
        AND t.location_id = ?
        AND t.issued_at >= ?
        AND t.issued_at <= COALESCE(?, datetime('now', 'localtime'))
        AND t.status = 'active'
      ORDER BY t.issued_at DESC
    `;

    const [sales, tickets] = await Promise.all([
      new Promise((resolve, reject) => {
        db.db.all(salesQuery, [
          targetUserId,
          targetLocationId,
          shift.shift_start_time,
          shift.actual_end_time || shift.shift_end_time
        ], (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        });
      }),
      new Promise((resolve, reject) => {
        db.db.all(ticketsQuery, [
          targetUserId,
          targetLocationId,
          shift.shift_start_time,
          shift.actual_end_time || shift.shift_end_time
        ], (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        });
      })
    ]);

    // Combine sales and tickets into one array
    const allTransactions = [...sales, ...tickets];

    console.log(`✅ SECURITY: Filtered shift report generated for location ${currentUser.location_name}`);
    console.log(`Report contains: Shift ${shift.shift_id} by ${shift.operator_name}, ${sales.length} sales, ${tickets.length} tickets`);

    return {
      success: true,
      data: {
        shift: shift,
        sales: sales,
        tickets: tickets,
        allTransactions: allTransactions
      }
    };
  } catch (error) {
    console.error('Error getting filtered shift report:', error);
    return { success: false, message: error.message };
  }
});

// Supabase Sync IPC Handlers
ipcMain.handle('manual-sync', async () => {
  try {
    if (!supabaseSync) {
      return { success: false, message: 'Supabase sync not initialized' };
    }

    console.log('🔄 Manual sync requested...');
    const result = await supabaseSync.manualSync();
    return result;
  } catch (error) {
    console.error('Error during manual sync:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-sync-status', async () => {
  try {
    if (!supabaseSync) {
      return {
        status: 'not_initialized',
        isSyncing: false,
        lastSyncTime: null,
        errors: [],
        autoSyncActive: false
      };
    }

    return supabaseSync.getSyncStatus();
  } catch (error) {
    console.error('Error getting sync status:', error);
    return {
      status: 'error',
      isSyncing: false,
      lastSyncTime: null,
      errors: [error.message],
      autoSyncActive: false
    };
  }
});

ipcMain.handle('toggle-auto-sync', async (event, enable) => {
  try {
    if (!supabaseSync) {
      return { success: false, message: 'Supabase sync not initialized' };
    }

    if (enable) {
      supabaseSync.startAutoSync();
      console.log('✅ Auto sync enabled');
      return { success: true, message: 'Auto sync enabled' };
    } else {
      supabaseSync.stopAutoSync();
      console.log('⏹️ Auto sync disabled');
      return { success: true, message: 'Auto sync disabled' };
    }
  } catch (error) {
    console.error('Error toggling auto sync:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('test-supabase-connection', async () => {
  try {
    if (!supabaseSync) {
      return { success: false, message: 'Supabase sync not initialized' };
    }

    return await supabaseSync.testConnection();
  } catch (error) {
    console.error('Error testing Supabase connection:', error);
    return { success: false, message: error.message };
  }
});

// Get shift sales report (for POS quick actions)
ipcMain.handle('get-shift-sales-report', async (event, { operatorFilter, periodFilter }) => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    const userId = currentUser.id;
    const locationId = currentUser.location_id;

    console.log('📊 Getting shift sales report for user:', userId, 'location:', locationId);

    // Get current shift
    const shift = await db.getCurrentShift(userId, locationId);

    if (!shift) {
      return { success: false, message: 'No active shift found' };
    }

    console.log('📊 Found active shift:', shift.shift_id);

    // Get sales for this shift
    const salesQuery = `
      SELECT s.*, 'sale' as record_type
      FROM sales s
      WHERE s.user_id = ? AND s.location_id = ?
        AND s.sale_date >= ? AND s.sale_date <= COALESCE(?, datetime('now', 'localtime'))
        AND s.status = 'completed'
      ORDER BY s.sale_date DESC
    `;

    // Get tickets for this shift
    const ticketsQuery = `
      SELECT t.*, 'ticket' as record_type, t.total_amount, t.payment_method as payment_type
      FROM tickets t
      WHERE t.user_id = ? AND t.location_id = ?
        AND t.issued_at >= ? AND t.issued_at <= COALESCE(?, datetime('now', 'localtime'))
        AND t.status = 'active'
      ORDER BY t.issued_at DESC
    `;

    const sales = await new Promise((resolve, reject) => {
      db.db.all(salesQuery, [userId, locationId, shift.shift_start_time, shift.shift_end_time], (err, rows) => {
        if (err) {
          console.error('Error fetching sales:', err);
          reject(err);
        } else {
          console.log('📊 Found', rows.length, 'sales records');
          resolve(rows);
        }
      });
    });

    const tickets = await new Promise((resolve, reject) => {
      db.db.all(ticketsQuery, [userId, locationId, shift.shift_start_time, shift.shift_end_time], (err, rows) => {
        if (err) {
          console.error('Error fetching tickets:', err);
          reject(err);
        } else {
          console.log('📊 Found', rows.length, 'ticket records');
          resolve(rows);
        }
      });
    });

    const allTransactions = [...sales, ...tickets];
    console.log('📊 Total transactions:', allTransactions.length);

    return {
      success: true,
      data: {
        shift,
        sales,
        tickets,
        allTransactions
      }
    };

  } catch (error) {
    console.error('❌ Error getting shift sales report:', error);
    return { success: false, message: error.message };
  }
});

// Printer Service IPC Handlers
ipcMain.handle('get-available-printers', () => {
  try {
    if (!printerService) {
      return { success: false, message: 'Printer service not initialized' };
    }

    return {
      success: true,
      printers: printerService.getAvailablePrinters(),
      defaultPrinter: printerService.getDefaultPrinter(),
      configuration: printerService.getConfiguration()
    };
  } catch (error) {
    console.error('❌ Error getting available printers:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('set-default-printer', (event, printerId) => {
  try {
    if (!printerService) {
      return { success: false, message: 'Printer service not initialized' };
    }

    const success = printerService.setDefaultPrinter(printerId);
    return { success, message: success ? 'Default printer set' : 'Printer not found' };
  } catch (error) {
    console.error('❌ Error setting default printer:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('print-receipt', async (event, saleData, options = {}) => {
  try {
    if (!printerService) {
      return { success: false, message: 'Printer service not initialized' };
    }

    console.log('🖨️ Print receipt request received:', saleData.sale_id);
    const result = await printerService.printReceipt(saleData, options);

    return result;
  } catch (error) {
    console.error('❌ Error printing receipt:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('test-printer', async (event, printerId = null) => {
  try {
    if (!printerService) {
      return { success: false, message: 'Printer service not initialized' };
    }

    console.log('🖨️ Test print request for printer:', printerId || 'default');
    const result = await printerService.testPrint(printerId);

    return result;
  } catch (error) {
    console.error('❌ Error testing printer:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('update-printer-config', (event, newConfig) => {
  try {
    if (!printerService) {
      return { success: false, message: 'Printer service not initialized' };
    }

    printerService.updateConfiguration(newConfig);
    return { success: true, message: 'Configuration updated' };
  } catch (error) {
    console.error('❌ Error updating printer configuration:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('refresh-printers', async () => {
  try {
    if (!printerService) {
      return { success: false, message: 'Printer service not initialized' };
    }

    console.log('🔄 Refreshing printer list...');
    await printerService.detectAllPrinters();

    // Re-detect system printers
    if (mainWindow && mainWindow.webContents) {
      try {
        const systemPrinters = await mainWindow.webContents.getPrinters();
        const formattedPrinters = systemPrinters.map(printer => ({
          id: `system_${printer.name.replace(/\s+/g, '_')}`,
          name: printer.name,
          type: 'system',
          connection: 'system',
          status: printer.status === 0 ? 'online' : 'offline',
          isDefault: printer.isDefault,
          description: printer.description || '',
          options: printer.options || {}
        }));

        printerService.systemPrinters = formattedPrinters;
        printerService.availablePrinters = [...printerService.systemPrinters, ...printerService.thermalPrinters];
      } catch (error) {
        console.error('❌ Error re-detecting system printers:', error);
      }
    }

    return {
      success: true,
      printers: printerService.getAvailablePrinters(),
      message: 'Printers refreshed successfully'
    };
  } catch (error) {
    console.error('❌ Error refreshing printers:', error);
    return { success: false, message: error.message };
  }
});

// Email Service IPC Handlers
ipcMain.handle('send-daily-sales-report-email', async (event, { reportData, csvContent, filters }) => {
  try {
    if (!emailService) {
      return { success: false, message: 'Email service not initialized' };
    }

    console.log('📧 Processing daily sales report email request...');
    const result = await emailService.sendDailySalesReport(reportData, csvContent, filters);

    return result;
  } catch (error) {
    console.error('❌ Error sending daily sales report email:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('send-shift-sales-report-email', async (event, { shiftData, csvContent }) => {
  try {
    if (!emailService) {
      return { success: false, message: 'Email service not initialized' };
    }

    console.log('📧 Processing shift sales report email request...');
    const result = await emailService.sendShiftSalesReport(shiftData, csvContent);

    return result;
  } catch (error) {
    console.error('❌ Error sending shift sales report email:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('test-email-service', async () => {
  try {
    if (!emailService) {
      return { success: false, message: 'Email service not initialized' };
    }

    console.log('📧 Testing email service connection...');
    const result = await emailService.testConnection();

    return result;
  } catch (error) {
    console.error('❌ Error testing email service:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-email-service-status', () => {
  try {
    if (!emailService) {
      return {
        enabled: false,
        configured: false,
        message: 'Email service not initialized'
      };
    }

    return {
      enabled: emailService.isEnabled,
      configured: emailService.config.apiKey && emailService.config.apiKey !== 're_xxxxxxxxx_your_actual_api_key_here',
      businessOwnerEmail: emailService.config.businessOwnerEmail,
      fromAddress: emailService.config.fromAddress,
      message: emailService.isEnabled ? 'Email service ready' : 'Email service disabled'
    };
  } catch (error) {
    console.error('❌ Error getting email service status:', error);
    return {
      enabled: false,
      configured: false,
      message: error.message
    };
  }
});
