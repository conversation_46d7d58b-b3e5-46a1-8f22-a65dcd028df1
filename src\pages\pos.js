const { ipc<PERSON><PERSON><PERSON> } = require('electron');

// Import day.js for time handling (same as tickets)
const dayjs = require('dayjs');

// Global state
let cartItems = [];
let currentInput = "";
let selectedItemIndex = 0;
let specialDiscount = false;
let discountAmount = 0;
let isEditingPrice = false;

// Products data - loaded from database
let demoProducts = [];

// Shift management
let currentShift = null;
let shiftTimer = null;
let shiftUpdateInterval = null;

// Load products from database
async function loadProducts() {
    try {
        console.log('POS - Loading products from database...');

        // Log current user context for security validation
        if (currentUser) {
            console.log(`POS - ✅ SECURITY: Loading products for user: ${currentUser.username} (${currentUser.role})`);
            console.log(`POS - ✅ SECURITY: User location: ${currentUser.location_name} (ID: ${currentUser.location_id})`);
        } else {
            console.warn('POS - ⚠️ SECURITY: No current user context when loading products');
        }

        const result = await ipcRenderer.invoke('get-pos-products');

        if (result.success && result.products) {
            demoProducts = result.products.map(product => ({
                id: product.id.toString(),
                name: product.name || product.description,
                barcode: product.barcode,
                price: product.price || 0, // Use price from products table (purchase_price)
                category: product.category || 'Uncategorized',
                subcategory: product.subcategory || 'General',
                daily_item: product.daily_item || 0, // Include daily_item field
                image: product.image || product.image_path || `https://via.placeholder.com/200x200/4CAF50/FFFFFF?text=${encodeURIComponent((product.name || product.description).substring(0, 10))}`
            }));

            console.log(`POS - ✅ SECURITY: Successfully loaded ${demoProducts.length} location-filtered products for ${currentUser?.location_name || 'Unknown location'}`);

            // Log sample products for validation (first 3)
            if (demoProducts.length > 0) {
                console.log('POS - ✅ SECURITY: Sample products with pricing:', demoProducts.slice(0, 3).map(p => ({
                    name: p.name,
                    id: p.id,
                    barcode: p.barcode,
                    price: p.price
                })));
                console.log('POS - 🔍 PRICING: Using purchase_price from products table');
            }

            // Only refresh UI if modal elements exist (modal is open)
            if (document.getElementById('categories-list')) {
                populateCategories();
            }
            if (document.getElementById('products-grid')) {
                populateProductsGrid();
            }
        } else {
            console.error('POS - ❌ SECURITY: Error loading products:', result.message || 'Unknown error');
            console.error('POS - ❌ SECURITY: Full result:', result);
            // Keep empty array if no products found
            demoProducts = [];
        }
    } catch (error) {
        console.error('POS - ❌ SECURITY: Exception loading products:', error);
        demoProducts = [];
    }
}

// Current user management
let currentUser = null;
let userPermissions = [];

async function loadCurrentUser() {
    try {
        console.log('POS - Loading current user...');
        currentUser = await ipcRenderer.invoke('get-current-user');

        if (currentUser) {
            // Extract permissions safely
            userPermissions = currentUser.permissions || [];

            console.log('POS - User loaded successfully:', {
                username: currentUser.username,
                role: currentUser.role,
                permissionCount: userPermissions.length
            });

            // Log permissions for debugging
            if (userPermissions.length > 0) {
                console.log('POS - User permissions:', userPermissions.map(p => p.module_id));
            }

            updateOperatorInfo();
        } else {
            console.error('POS - No current user returned - this should not happen');
            // DO NOT set fallback admin - this compromises security
            currentUser = null;
            userPermissions = [];
            document.getElementById('current-operator').textContent = 'Error: No User';
        }
    } catch (error) {
        console.error('Error loading current user in POS:', error);
        // DO NOT set fallback admin - this compromises security
        currentUser = null;
        userPermissions = [];
        document.getElementById('current-operator').textContent = 'Error: User Load Failed';
    }
}

function updateOperatorInfo() {
    if (currentUser) {
        const operatorSpan = document.getElementById('current-operator');
        if (operatorSpan) {
            operatorSpan.textContent = currentUser.name || currentUser.username || 'Unknown';
        }

        // Update location name in header
        const locationNameElement = document.getElementById('location-name');
        if (locationNameElement) {
            if (currentUser.location_name) {
                locationNameElement.textContent = currentUser.location_name;
            } else {
                locationNameElement.textContent = 'Rainbow Station Inc.';
            }
        }

        // Update location security indicator
        updateLocationSecurityIndicator();
    }
}

function updateLocationSecurityIndicator() {
    const indicator = document.getElementById('location-access-status');
    const indicatorContainer = document.querySelector('.location-security-indicator');

    if (indicator && indicatorContainer) {
        if (currentUser && currentUser.location_id && currentUser.location_name) {
            indicator.textContent = `Secure (${currentUser.location_name})`;
            indicatorContainer.className = 'location-security-indicator';
        } else if (currentUser && !currentUser.location_id) {
            indicator.textContent = 'No Location Assigned';
            indicatorContainer.className = 'location-security-indicator error';
        } else {
            indicator.textContent = 'Not Authenticated';
            indicatorContainer.className = 'location-security-indicator error';
        }
    }
}

// Permission checking functions - RESTRICTIVE (deny by default)
function hasModuleAccess(moduleId) {
    try {
        // If no current user loaded, DENY access
        if (!currentUser) {
            console.log('POS - No current user loaded, DENYING access to', moduleId);
            return false;
        }

        // Admin has access to everything
        if (currentUser.role === 'Admin') {
            console.log('POS - Admin user, allowing access to', moduleId);
            return true;
        }

        // Check if user has permission for this module
        const hasAccess = userPermissions && userPermissions.some(perm => perm.module_id === moduleId);
        console.log(`POS - Access check for ${moduleId}: ${hasAccess} (user: ${currentUser.username}, role: ${currentUser.role})`);

        return hasAccess;
    } catch (error) {
        console.error('Error checking module access:', error);
        // Default to DENYING access if there's an error
        return false;
    }
}

function canAccessAdmin() {
    return hasModuleAccess('dashboard') || hasModuleAccess('master') || hasModuleAccess('reports') ||
           hasModuleAccess('transactions') || hasModuleAccess('wholesale') || hasModuleAccess('user-management');
}

function canAccessTheater() {
    return hasModuleAccess('theater');
}

// Apply access control restrictions to POS interface
function applyAccessControlRestrictions() {
    console.log('POS - Applying access control restrictions...');

    if (!currentUser) {
        console.warn('POS - No current user, cannot apply restrictions');
        return;
    }

    restrictAdminButton();
    restrictTheaterButton();

    console.log('POS - Access control restrictions applied');
}

function restrictAdminButton() {
    const adminBtn = document.querySelector('button[onclick="navigateToAdmin()"]');

    if (adminBtn) {
        if (!canAccessAdmin()) {
            console.log('POS - Restricting admin button access');
            adminBtn.style.opacity = '0.5';
            adminBtn.style.cursor = 'not-allowed';
            adminBtn.style.pointerEvents = 'none';
            adminBtn.title = 'Access Denied: You do not have permission to access Admin Panel';

            // Replace onclick with access denied message
            adminBtn.removeAttribute('onclick');
            adminBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                showAccessDeniedMessage('Admin Panel');
            });
        } else {
            console.log('POS - Admin button access allowed');
        }
    }
}

function restrictTheaterButton() {
    const theaterBtn = document.querySelector('button[onclick="navigateToTheater()"]');

    if (theaterBtn) {
        if (!canAccessTheater()) {
            console.log('POS - Restricting theater button access');
            theaterBtn.style.opacity = '0.5';
            theaterBtn.style.cursor = 'not-allowed';
            theaterBtn.style.pointerEvents = 'none';
            theaterBtn.title = 'Access Denied: You do not have permission to access Theater Management';

            // Replace onclick with access denied message
            theaterBtn.removeAttribute('onclick');
            theaterBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                showAccessDeniedMessage('Theater Management');
            });
        } else {
            console.log('POS - Theater button access allowed');
        }
    }
}

function showAccessDeniedMessage(moduleName) {
    alert(`Access Denied: You do not have permission to access ${moduleName}.`);
}

// Add keyboard support for barcode scanning
document.addEventListener('keydown', (event) => {
    // Only handle keyboard input when not in a modal or input field
    if (document.querySelector('.modal.show') || event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
        return;
    }

    // Handle number keys (0-9)
    if (event.key >= '0' && event.key <= '9') {
        event.preventDefault();
        handleNumberClick(event.key);
    }
    // Handle decimal point
    else if (event.key === '.') {
        event.preventDefault();
        handleNumberClick('.');
    }
    // Handle Enter key
    else if (event.key === 'Enter') {
        event.preventDefault();
        handleEnter();
    }
    // Handle Backspace
    else if (event.key === 'Backspace') {
        event.preventDefault();
        handleBackspace();
    }
    // Handle Escape (Clear)
    else if (event.key === 'Escape') {
        event.preventDefault();
        handleClear();
    }
});

// Initialize simple barcode scanner service
let barcodeScanner = null;

// Initialize customer display service
let customerDisplay = null;

// Initialize cash drawer service
let cashDrawer = null;

// Initialize the application
document.addEventListener('DOMContentLoaded', async function() {
    console.log('POS - DOM Content Loaded, initializing...');

    updateTime();
    setInterval(updateTime, 1000);
    updateDisplay();
    updateTotals();

    // Load current user information first
    await loadCurrentUser();

    // Load products from database
    await loadProducts();

    console.log('POS - User loading completed, permissions ready');

    // Apply access control restrictions
    applyAccessControlRestrictions();

    // Initialize shift management
    await initializeShiftManagement();

    // Initialize maximize button state
    try {
        const isMaximized = await ipcRenderer.invoke('is-fullscreen');
        const fullscreenBtn = document.getElementById('fullscreen-btn');
        if (fullscreenBtn) {
            fullscreenBtn.textContent = isMaximized ? '🗗' : '⛶';
            fullscreenBtn.title = isMaximized ? 'Restore Window' : 'Maximize Window';
        }
    } catch (error) {
        console.error('Error checking initial maximize state:', error);
    }

    console.log('POS - Initialization completed successfully');

    // Initialize barcode scanner
    initializeBarcodeScanner();

    // Initialize customer display
    initializeCustomerDisplay();

    // Initialize cash drawer
    initializeCashDrawer();
});

// Time functions
function updateTime() {
    const now = new Date();
    document.getElementById('current-date').textContent = formatDate(now);
    document.getElementById('current-time').textContent = formatTime(now);
}

function formatDate(date) {
    return date.toLocaleDateString("en-US", { month: "2-digit", day: "2-digit", year: "2-digit" });
}

function formatTime(date) {
    return date.toLocaleTimeString("en-US", { hour12: false });
}

// Initialize simple barcode scanner service
function initializeBarcodeScanner() {
    try {
        console.log('🔍 Initializing simple barcode scanner service...');

        // Create simple barcode scanner instance
        barcodeScanner = new SimpleBarcodeScanner();

        // Configure scanner settings
        barcodeScanner.configure({
            timeBeforeScanTest: 150,     // ms - time to wait for complete scan
            avgTimeByChar: 30,           // ms - average time between characters
            minLength: 3,                // minimum barcode length
            maxLength: 50,               // maximum barcode length
            suffixKeyCodes: [13],        // Enter key at end of scan
            reactToKeydown: true,        // Listen to keyboard events
            reactToPaste: true,          // Support paste mode scanners
            ignoreIfFocusOn: 'input, textarea, select', // Ignore when input focused
            debug: false                 // Set to true for debugging
        });

        // Register barcode scan callback
        barcodeScanner.onBarcodeScanned((scanData) => {
            console.log('🔍 Barcode scanned:', scanData);
            handleBarcodeScanned(scanData);
        });

        // Initialize the scanner (attach to document)
        const result = barcodeScanner.initialize(document);

        if (result.success) {
            console.log(`✅ Barcode scanner initialized successfully (${result.method})`);

            // Add visual indicator
            addBarcodeScannerIndicator(result.method);
        } else {
            console.error('❌ Failed to initialize barcode scanner:', result.error);
        }

    } catch (error) {
        console.error('❌ Failed to initialize barcode scanner:', error);
    }
}

// Handle barcode scanned event
function handleBarcodeScanned(scanData) {
    const { barcode, source, quantity } = scanData;

    console.log(`📱 Processing barcode: "${barcode}" (${source})`);

    // Update the display to show the scanned barcode
    currentInput = barcode;
    updateDisplay();

    // Show visual feedback
    showBarcodeScanFeedback(barcode, source);

    // Automatically process the barcode after a short delay
    setTimeout(() => {
        scanBarcode(barcode);
    }, 100);
}

// Show visual feedback for barcode scan
function showBarcodeScanFeedback(barcode, source) {
    const display = document.getElementById('current-input');
    const originalColor = display.style.color;

    // Show scan feedback with different colors for different sources
    if (source === 'scanner' || source === 'onscan.js') {
        display.style.color = '#00ccff';  // Blue for hardware scanner
    } else if (source === 'paste') {
        display.style.color = '#00ff88';  // Green for paste
    } else {
        display.style.color = '#ffcc00';  // Yellow for fallback/manual
    }

    display.style.fontWeight = 'bold';

    // Update barcode indicator
    const indicator = document.getElementById('barcode-indicator');
    if (indicator) {
        if (source === 'scanner' || source === 'onscan.js') {
            indicator.textContent = '📱 SCANNER';
            indicator.style.color = '#00ccff';
        } else if (source === 'paste') {
            indicator.textContent = '📋 PASTE';
            indicator.style.color = '#00ff88';
        } else {
            indicator.textContent = '⌨️ MANUAL';
            indicator.style.color = '#ffcc00';
        }
        indicator.style.display = 'inline';
    }

    // Reset after processing
    setTimeout(() => {
        display.style.color = originalColor;
        display.style.fontWeight = 'normal';
        if (indicator) {
            indicator.style.display = 'none';
        }
    }, 2000);
}

// Add visual indicator for barcode scanner status
function addBarcodeScannerIndicator(method = 'unknown') {
    const rightPanel = document.querySelector('.right-panel');
    if (rightPanel) {
        const indicator = document.createElement('div');
        indicator.id = 'scanner-status-indicator';
        indicator.style.cssText = `
            background: linear-gradient(135deg, #1a1a1a, #2a2a2a);
            border: 1px solid #00ff00;
            border-radius: 6px;
            padding: 6px 12px;
            margin-top: 8px;
            text-align: center;
            font-size: 11px;
            color: #00ff00;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
        `;

        const methodText = method === 'onscan.js' ? 'OnScan.js' :
                          method === 'fallback' ? 'Fallback' : 'Ready';

        indicator.innerHTML = `
            <span style="color: #00ff00;">●</span>
            <span>SCANNER ${methodText.toUpperCase()}</span>
            <span style="font-size: 10px; opacity: 0.7;">📱</span>
        `;

        // Insert after the barcode instructions
        const instructions = rightPanel.querySelector('div[style*="BARCODE SCANNING"]');
        if (instructions) {
            instructions.parentNode.insertBefore(indicator, instructions.nextSibling);
        } else {
            rightPanel.appendChild(indicator);
        }
    }
}

// Initialize customer display service
function initializeCustomerDisplay() {
    try {
        console.log('🖥️ Initializing customer display service...');

        // Create customer display instance
        customerDisplay = new CustomerDisplayService();

        // Configure display settings
        customerDisplay.updateConfig({
            port: 'COM3',           // Adjust based on your setup
            baudRate: 9600,
            autoConnect: true,
            scrollSpeed: 500,
            brightness: 100
        });

        // Initialize the display
        customerDisplay.initialize().then(result => {
            if (result.success) {
                console.log(`✅ Customer display initialized successfully (${result.displayType})`);

                // Show welcome message
                customerDisplay.showWelcome();

                // Add visual indicator
                addCustomerDisplayIndicator(result.connected);
            } else {
                console.warn('⚠️ Customer display initialization failed:', result.error);
                addCustomerDisplayIndicator(false);
            }
        }).catch(error => {
            console.error('❌ Customer display initialization error:', error);
            addCustomerDisplayIndicator(false);
        });

    } catch (error) {
        console.error('❌ Failed to initialize customer display:', error);
        addCustomerDisplayIndicator(false);
    }
}

// Add visual indicator for customer display status
function addCustomerDisplayIndicator(connected) {
    const rightPanel = document.querySelector('.right-panel');
    if (rightPanel) {
        const indicator = document.createElement('div');
        indicator.id = 'display-status-indicator';
        indicator.style.cssText = `
            background: linear-gradient(135deg, #1a1a1a, #2a2a2a);
            border: 1px solid ${connected ? '#00ff00' : '#ff6600'};
            border-radius: 6px;
            padding: 6px 12px;
            margin-top: 8px;
            text-align: center;
            font-size: 11px;
            color: ${connected ? '#00ff00' : '#ff6600'};
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
        `;

        indicator.innerHTML = `
            <span style="color: ${connected ? '#00ff00' : '#ff6600'};">●</span>
            <span>DISPLAY ${connected ? 'CONNECTED' : 'OFFLINE'}</span>
            <span style="font-size: 10px; opacity: 0.7;">🖥️</span>
        `;

        // Insert after scanner indicator
        const scannerIndicator = document.getElementById('scanner-status-indicator');
        if (scannerIndicator) {
            scannerIndicator.parentNode.insertBefore(indicator, scannerIndicator.nextSibling);
        } else {
            rightPanel.appendChild(indicator);
        }
    }
}

// Update customer display with current transaction
function updateCustomerDisplay() {
    if (!customerDisplay || !customerDisplay.isConnected) {
        return;
    }

    try {
        const transactionData = {
            items: cartItems,
            subtotal: calculateSubtotal(),
            tax: calculateTax(),
            total: calculateTotal()
        };

        customerDisplay.showTransaction(transactionData);

    } catch (error) {
        console.error('❌ Error updating customer display:', error);
    }
}

// Initialize cash drawer service
function initializeCashDrawer() {
    try {
        console.log('💰 Initializing cash drawer service...');

        // Create cash drawer instance
        cashDrawer = new CashDrawerService();

        // Configure cash drawer settings
        cashDrawer.updateConfig({
            serialPort: 'COM1',        // Adjust based on your setup
            baudRate: 9600,
            drawerNumber: 0,           // 0 = Drawer 1, 1 = Drawer 2
            pulseDuration: 100,        // 100ms pulse duration
            pulseDelay: 100,           // 100ms delay before pulse
            connectionMethods: ['printer', 'serial', 'usb'], // Try in this order
            autoDetect: true,          // Auto-detect connection method
            testOnConnect: false,      // Don't test on startup (avoid opening drawer)
            maxRetries: 3
        });

        // Initialize the cash drawer (pass printer service for integration)
        cashDrawer.initialize(printerService).then(result => {
            if (result.success) {
                console.log(`✅ Cash drawer initialized successfully (${result.method || 'ready'})`);

                // Add visual indicator
                addCashDrawerIndicator(result.connected, result.method);
            } else {
                console.warn('⚠️ Cash drawer initialization failed:', result.error);
                addCashDrawerIndicator(false, 'error');
            }
        }).catch(error => {
            console.error('❌ Cash drawer initialization error:', error);
            addCashDrawerIndicator(false, 'error');
        });

    } catch (error) {
        console.error('❌ Failed to initialize cash drawer:', error);
        addCashDrawerIndicator(false, 'error');
    }
}

// Add visual indicator for cash drawer status
function addCashDrawerIndicator(connected, method = 'unknown') {
    const rightPanel = document.querySelector('.right-panel');
    if (rightPanel) {
        const indicator = document.createElement('div');
        indicator.id = 'cash-drawer-status-indicator';
        indicator.style.cssText = `
            background: linear-gradient(135deg, #1a1a1a, #2a2a2a);
            border: 1px solid ${connected ? '#00ff00' : '#ff6600'};
            border-radius: 6px;
            padding: 6px 12px;
            margin-top: 8px;
            text-align: center;
            font-size: 11px;
            color: ${connected ? '#00ff00' : '#ff6600'};
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
        `;

        const statusText = connected ? 'CONNECTED' :
                          method === 'error' ? 'ERROR' : 'OFFLINE';
        const methodText = connected && method !== 'unknown' ? ` (${method.toUpperCase()})` : '';

        indicator.innerHTML = `
            <span style="color: ${connected ? '#00ff00' : '#ff6600'};">●</span>
            <span>DRAWER ${statusText}${methodText}</span>
            <span style="font-size: 10px; opacity: 0.7;">💰</span>
        `;

        // Insert after display indicator
        const displayIndicator = document.getElementById('display-status-indicator');
        if (displayIndicator) {
            displayIndicator.parentNode.insertBefore(indicator, displayIndicator.nextSibling);
        } else {
            rightPanel.appendChild(indicator);
        }
    }
}

// Reset cart to empty state
function resetCart() {
    console.log('🔄 Resetting cart to empty state...');

    cartItems = [];
    currentInput = "";
    selectedItemIndex = 0;
    discountAmount = 0;
    specialDiscount = false;
    isEditingPrice = false;
    selectedPaymentMethod = 'Cash'; // Reset payment method

    // Reset payment amounts
    paymentAmounts = {
        Cash: 0,
        Debit: 0,
        Credit: 0
    };

    // Update all displays
    updateCartDisplay();
    updateTotals();
    updateDisplay();

    // Update customer display to show welcome message
    if (customerDisplay && customerDisplay.isConnected) {
        customerDisplay.showWelcome();
    }

    console.log('✅ Cart reset completed');
}

// Debug inventory system
async function debugInventory() {
    try {
        console.log('🔍 DEBUG: Checking inventory system...');

        const result = await ipcRenderer.invoke('debug-inventory');

        if (result.success) {
            console.log('📦 DEBUG Results:', result.data);

            // Show debug info in alert
            let debugMessage = `INVENTORY DEBUG RESULTS:\n\n`;
            debugMessage += `User Location: ${result.data.user_location}\n`;
            debugMessage += `Products Checked: ${result.data.products.length}\n`;
            debugMessage += `Reorder Alerts: ${result.data.reorder_report.alerts.length}\n`;
            debugMessage += `Critical Stock: ${result.data.reorder_report.critical_stock.length}\n`;
            debugMessage += `Low Stock: ${result.data.reorder_report.low_stock.length}\n\n`;

            if (result.data.products.length > 0) {
                debugMessage += `SAMPLE PRODUCTS:\n`;
                result.data.products.forEach(product => {
                    debugMessage += `- ${product.name} (ID: ${product.id})\n`;
                    debugMessage += `  Min: ${product.min_qty}, Max: ${product.max_qty}\n`;
                    if (product.location_stocks.length > 0) {
                        product.location_stocks.forEach(stock => {
                            debugMessage += `  Stock at ${stock.location}: ${stock.stock}\n`;
                        });
                    } else {
                        debugMessage += `  No location stock records\n`;
                    }
                    debugMessage += `\n`;
                });
            }

            if (result.data.reorder_report.alerts.length > 0) {
                debugMessage += `REORDER ALERTS:\n`;
                result.data.reorder_report.alerts.forEach(alert => {
                    debugMessage += `- ${alert.description}\n`;
                    debugMessage += `  Current: ${alert.stock}, Min: ${alert.min_qty}\n`;
                    debugMessage += `  Status: ${alert.stock_status.toUpperCase()}\n\n`;
                });
            }

            alert(debugMessage);
        } else {
            alert('Debug failed: ' + result.message);
        }

    } catch (error) {
        console.error('❌ Debug error:', error);
        alert('Debug error: ' + error.message);
    }
}

function formatCurrency(amount) {
    return amount.toFixed(2);
}

// Navigation functions with permission checks
function navigateToAdmin() {
    if (canAccessAdmin()) {
        console.log('POS - Navigating to admin (permission granted)');
        ipcRenderer.invoke('navigate-to', 'admin');
    } else {
        console.log('POS - Admin navigation blocked (no permission)');
        showAccessDeniedMessage('Admin Panel');
    }
}

function navigateToTheater() {
    if (canAccessTheater()) {
        console.log('POS - Navigating to theater (permission granted)');
        ipcRenderer.invoke('navigate-to', 'theater');
    } else {
        console.log('POS - Theater navigation blocked (no permission)');
        showAccessDeniedMessage('Theater Management');
    }
}

// Logout function
function logout() {
    if (confirm('Are you sure you want to logout?')) {
        // Clear any local data/state if needed
        try {
            ipcRenderer.invoke('logout').then(() => {
                console.log('Logout successful');
            }).catch((error) => {
                console.error('Logout error:', error);
            });
        } catch (error) {
            console.error('Logout error:', error);
        }
    }
}

// Window control functions
function closeApp() {
    ipcRenderer.invoke('close-app');
}

function minimizeApp() {
    ipcRenderer.invoke('minimize-app');
}

async function toggleFullscreen() {
    try {
        console.log('Toggling maximize...');
        const isMaximized = await ipcRenderer.invoke('toggle-fullscreen');
        console.log('Maximized state:', isMaximized);
        const fullscreenBtn = document.getElementById('fullscreen-btn');
        if (fullscreenBtn) {
            fullscreenBtn.textContent = isMaximized ? '🗗' : '⛶';
            fullscreenBtn.title = isMaximized ? 'Restore Window' : 'Maximize Window';
            console.log('Updated button text to:', fullscreenBtn.textContent);
        } else {
            console.error('Maximize button not found');
        }
    } catch (error) {
        console.error('Error toggling maximize:', error);
    }
}

// Keypad functions
function handleNumberClick(num) {
    // Handle decimal point - only allow one decimal point
    if (num === '.') {
        if (currentInput.includes('.')) {
            return; // Don't add another decimal point
        }
        // If currentInput is empty or just "0", start with "0."
        if (currentInput === '' || currentInput === '0') {
            currentInput = '0.';
        } else {
            currentInput += '.';
        }
    } else {
        // Handle regular numbers
        currentInput += num;
    }
    updateDisplay();
}

function handleBackspace() {
    currentInput = currentInput.slice(0, -1);
    updateDisplay();
}

function handleClear() {
    currentInput = "";
    isEditingPrice = false;
    updateDisplay();
}

function handleEnter() {
    if (currentInput) {
        console.log(`🔍 ENTER pressed with input: "${currentInput}"`);
        console.log(`📊 Current state: cartItems=${cartItems.length}, selectedItemIndex=${selectedItemIndex}, isEditingPrice=${isEditingPrice}`);

        // Check if we're specifically editing price
        if (isEditingPrice && cartItems.length > 0 && selectedItemIndex >= 0) {
            console.log('💰 Editing price mode');
            // Update price of selected item
            const newPrice = parseFloat(currentInput);
            if (newPrice > 0 && selectedItemIndex < cartItems.length) {
                cartItems[selectedItemIndex].price = newPrice;
                cartItems[selectedItemIndex].value = cartItems[selectedItemIndex].quantity * newPrice;
                updateCartDisplay();
                updateTotals();
                console.log(`✅ Price updated to $${newPrice}`);
            }
            isEditingPrice = false;
        }
        // Check if we're editing quantity (cart has items, item selected, but not editing price)
        else if (cartItems.length > 0 && selectedItemIndex >= 0 && !isEditingPrice) {
            console.log('🔢 Editing quantity mode');
            // Check if input looks like a quantity (small number) vs barcode (longer number/string)
            const inputValue = currentInput.trim();
            const isLikelyQuantity = /^\d{1,3}$/.test(inputValue) && parseInt(inputValue) <= 999;

            if (isLikelyQuantity) {
                // Update quantity of selected item
                const quantity = parseInt(inputValue);
                if (quantity > 0 && selectedItemIndex < cartItems.length) {
                    cartItems[selectedItemIndex].quantity = quantity;
                    cartItems[selectedItemIndex].value = quantity * cartItems[selectedItemIndex].price;
                    updateCartDisplay();
                    updateTotals();
                    console.log(`✅ Quantity updated to ${quantity}`);
                }
            } else {
                // Treat as barcode even if item is selected
                console.log('📱 Treating as barcode (longer input)');
                scanBarcode(inputValue);
            }
        }
        // Default: Try barcode scanning
        else {
            console.log('📱 Barcode scanning mode');
            const barcode = currentInput.trim();
            scanBarcode(barcode);
        }

        currentInput = "";
        updateDisplay();
    }
}

// Separate function for barcode scanning logic
function scanBarcode(barcode) {
    console.log(`🔍 Scanning barcode: "${barcode}"`);
    const product = findProductByBarcode(barcode);

    if (product) {
        console.log(`✅ Barcode scan successful: Found product "${product.name}" (${barcode})`);
        addItemToCart(product);

        // Show success feedback
        showBarcodeSuccessMessage(product.name, barcode);
    } else {
        console.log(`❌ Barcode scan failed: No product found for barcode "${barcode}"`);
        console.log(`📋 Available products with barcodes:`, demoProducts.filter(p => p.barcode).map(p => ({name: p.name, barcode: p.barcode})).slice(0, 5));

        // Show error feedback
        showBarcodeErrorMessage(barcode);
    }
}

// Find product by barcode
function findProductByBarcode(barcode) {
    console.log(`🔍 Searching for barcode: "${barcode}"`);
    console.log(`📦 Total products available: ${demoProducts ? demoProducts.length : 0}`);

    if (!barcode || !demoProducts || demoProducts.length === 0) {
        console.log('❌ No barcode provided or no products loaded');
        return null;
    }

    const searchBarcode = barcode.toString().toLowerCase().trim();
    console.log(`🔍 Normalized search barcode: "${searchBarcode}"`);

    // Log some sample barcodes for debugging
    const productsWithBarcodes = demoProducts.filter(p => p.barcode);
    console.log(`📋 Products with barcodes: ${productsWithBarcodes.length}`);
    if (productsWithBarcodes.length > 0) {
        console.log(`📋 Sample barcodes:`, productsWithBarcodes.slice(0, 5).map(p => ({
            name: p.name,
            barcode: p.barcode
        })));
    }

    // First try exact match
    let product = demoProducts.find(p =>
        p.barcode && p.barcode.toString().toLowerCase() === searchBarcode
    );

    if (product) {
        console.log(`✅ Exact match found: ${product.name}`);
        return product;
    }

    // If no exact match, try partial match (barcode contains the search term)
    product = demoProducts.find(p =>
        p.barcode && p.barcode.toString().toLowerCase().includes(searchBarcode)
    );

    if (product) {
        console.log(`✅ Partial match found: ${product.name}`);
        return product;
    }

    // If still no match, try reverse (search term contains barcode)
    if (searchBarcode.length > 3) {
        product = demoProducts.find(p =>
            p.barcode && searchBarcode.includes(p.barcode.toString().toLowerCase())
        );

        if (product) {
            console.log(`✅ Reverse match found: ${product.name}`);
            return product;
        }
    }

    console.log(`❌ No product found for barcode: "${searchBarcode}"`);
    return null;
}

// Show success message when barcode is found
function showBarcodeSuccessMessage(productName, barcode) {
    const display = document.getElementById('current-input');

    // Show success message
    display.style.color = '#00ff00';
    display.textContent = `✅ ADDED: ${productName}`;

    // Play success sound (optional)
    playBarcodeSound('success');

    // Reset after 2 seconds
    setTimeout(() => {
        display.style.color = '#00ff00';
        display.textContent = '0';
    }, 2000);
}

// Show error message when barcode is not found
function showBarcodeErrorMessage(barcode) {
    const display = document.getElementById('current-input');

    // Show error message
    display.style.color = '#ff0000';
    display.textContent = `❌ NOT FOUND: ${barcode}`;

    // Play error sound (optional)
    playBarcodeSound('error');

    // Reset after 3 seconds
    setTimeout(() => {
        display.style.color = '#00ff00';
        display.textContent = '0';
    }, 3000);
}

// Play barcode scan sound feedback
function playBarcodeSound(type) {
    try {
        // Create audio context for beep sounds
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        if (type === 'success') {
            // Success: Higher pitch, shorter beep
            oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.1);
        } else if (type === 'error') {
            // Error: Lower pitch, longer beep
            oscillator.frequency.setValueAtTime(300, audioContext.currentTime);
            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.3);
        }
    } catch (error) {
        // Audio not supported or blocked, silently continue
        console.log('Audio feedback not available:', error.message);
    }
}

function handleEditPrice() {
    if (cartItems.length > 0 && selectedItemIndex < cartItems.length) {
        isEditingPrice = true;
        currentInput = cartItems[selectedItemIndex].price.toString();
        updateDisplay();
    }
}

function handleRemoveItem() {
    if (cartItems.length > 0 && selectedItemIndex < cartItems.length) {
        cartItems.splice(selectedItemIndex, 1);
        if (selectedItemIndex >= cartItems.length) {
            selectedItemIndex = Math.max(0, cartItems.length - 1);
        }
        updateCartDisplay();
        updateTotals();
    }
}

// Display functions
function updateDisplay() {
    const displayText = currentInput || "0";
    document.getElementById('current-input').textContent = displayText;

    const priceIndicator = document.getElementById('price-indicator');
    const barcodeIndicator = document.getElementById('barcode-indicator');

    if (isEditingPrice) {
        priceIndicator.style.display = 'inline';
        barcodeIndicator.style.display = 'none';
    } else {
        priceIndicator.style.display = 'none';

        // Show barcode indicator when cart is empty or no item is selected
        if (cartItems.length === 0 || selectedItemIndex < 0) {
            barcodeIndicator.style.display = 'inline';
        } else {
            barcodeIndicator.style.display = 'none';
        }
    }
}

// Cart functions
function addItemToCart(product) {
    const existingItemIndex = cartItems.findIndex(item => item.description === product.name);

    if (existingItemIndex >= 0) {
        // If item already exists, increase quantity
        cartItems[existingItemIndex].quantity += 1;
        cartItems[existingItemIndex].value = cartItems[existingItemIndex].quantity * cartItems[existingItemIndex].price;
    } else {
        // Add new item
        const newItem = {
            id: Date.now().toString(),
            qh: cartItems.length,
            description: product.name,
            quantity: 1,
            price: product.price,
            discount: 0,
            value: product.price,
            // Store additional product details for sales recording
            productId: product.id,
            barcode: product.barcode || '',
            category: product.category || '',
            subcategory: product.subcategory || '',
            isDeliItem: product.daily_item === 1 || product.daily_item === true
        };
        cartItems.push(newItem);
    }
    
    updateCartDisplay();
    updateTotals();

    // Update customer display
    updateCustomerDisplay();
}

function removeItemFromCart(product) {
    const existingItemIndex = cartItems.findIndex(item => item.description === product.name);

    if (existingItemIndex >= 0) {
        if (cartItems[existingItemIndex].quantity > 1) {
            // Decrease quantity
            cartItems[existingItemIndex].quantity -= 1;
            cartItems[existingItemIndex].value = cartItems[existingItemIndex].quantity * cartItems[existingItemIndex].price;
        } else {
            // Remove item completely
            cartItems.splice(existingItemIndex, 1);
            if (selectedItemIndex >= cartItems.length) {
                selectedItemIndex = Math.max(0, cartItems.length - 1);
            }
        }
        updateCartDisplay();
        updateTotals();
    }
}

function updateCartDisplay() {
    const tbody = document.getElementById('cart-items');

    if (cartItems.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="empty-cart">
                    No items in cart. Click SELECT ITEM to add products.
                </td>
            </tr>
        `;
    } else {
        tbody.innerHTML = cartItems.map((item, index) => `
            <tr class="${selectedItemIndex === index ? 'selected' : ''}" onclick="selectItem(${index})">
                <td>${index}</td>
                <td>${item.description}</td>
                <td>
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <button onclick="event.stopPropagation(); changeQuantity(${index}, -1)"
                                style="background: #aa0000; color: white; border: 1px solid #ff0000; width: 24px; height: 24px; border-radius: 4px; cursor: pointer; font-size: 14px; font-weight: bold;">-</button>
                        <span style="min-width: 30px; text-align: center; font-weight: bold;">${item.quantity}</span>
                        <button onclick="event.stopPropagation(); changeQuantity(${index}, 1)"
                                style="background: #00aa00; color: white; border: 1px solid #00ff00; width: 24px; height: 24px; border-radius: 4px; cursor: pointer; font-size: 14px; font-weight: bold;">+</button>
                    </div>
                </td>
                <td>${formatCurrency(item.price)}</td>
                <td>${formatCurrency(item.discount)}</td>
                <td>${formatCurrency(item.value)}</td>
            </tr>
        `).join('');
    }
}

function selectItem(index) {
    selectedItemIndex = index;
    updateCartDisplay();
}

// Function to change quantity directly from cart
function changeQuantity(index, change) {
    if (index >= 0 && index < cartItems.length) {
        const newQuantity = cartItems[index].quantity + change;

        if (newQuantity > 0) {
            cartItems[index].quantity = newQuantity;
            cartItems[index].value = newQuantity * cartItems[index].price;
            updateCartDisplay();
            updateTotals();
        } else if (newQuantity === 0) {
            // Remove item if quantity becomes 0
            cartItems.splice(index, 1);
            if (selectedItemIndex >= cartItems.length) {
                selectedItemIndex = Math.max(0, cartItems.length - 1);
            }
            updateCartDisplay();
            updateTotals();
        }
    }
}

// Calculation functions
function calculateSubtotal() {
    return cartItems.reduce((sum, item) => sum + item.value, 0);
}

function calculateTax() {
    return calculateSubtotal() * 0.06625;
}

function calculateTotal() {
    return calculateSubtotal() + calculateTax() - discountAmount;
}

function updateTotals() {
    document.getElementById('item-discount').textContent = formatCurrency(0.0);
    document.getElementById('discount-amount').textContent = formatCurrency(discountAmount);
    document.getElementById('subtotal').textContent = formatCurrency(calculateSubtotal());
    document.getElementById('tax-amount').textContent = formatCurrency(calculateTax());
    document.getElementById('item-count').textContent = cartItems.length;
    document.getElementById('cash-amount').textContent = formatCurrency(calculateTotal());
    document.getElementById('change-amount').textContent = formatCurrency(0.00);
    document.getElementById('total-amount').textContent = formatCurrency(calculateTotal());

    // Update modal footer if modal is open
    updateModalFooter();
}

// Checkout function
function handleCheckout() {
    if (cartItems.length > 0) {
        showCheckoutConfirmationModal();
    }
}

// Global variable to store selected payment method
let selectedPaymentMethod = 'Cash';

// Global variables for payment tracking
let currentPaymentAmount = '';
let paymentAmounts = {
    Cash: 0,
    Debit: 0,
    Credit: 0
};

function showCheckoutConfirmationModal() {
    // Create checkout confirmation modal
    const modal = document.createElement('div');
    modal.className = 'modal show';
    modal.id = 'checkout-confirmation-modal';

    // Reset payment amounts
    currentPaymentAmount = '';
    paymentAmounts = {
        Cash: calculateTotal(),
        Debit: 0,
        Credit: 0
    };

    // Generate product list HTML for display area
    const productListHTML = cartItems.map(item => `
        <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 12px; border-bottom: 1px solid #333; background-color: #1a1a1a; margin-bottom: 4px; border-radius: 4px;">
            <div style="flex: 1;">
                <div style="font-size: 16px; font-weight: bold; color: #00ff00;">${item.description}</div>
                <div style="font-size: 14px; color: #00cc00;">$${formatCurrency(item.price)} × ${item.quantity}</div>
            </div>
            <div style="text-align: right;">
                <div style="font-size: 18px; font-weight: bold; color: #00ff00;">$${formatCurrency(item.value)}</div>
            </div>
        </div>
    `).join('');

    modal.innerHTML = `
        <div class="modal-content" style="background-color: #000000; border: 2px solid #00ff00; color: #00ff00; width: 98vw; height: 95vh; max-width: 1400px; max-height: 95vh; overflow: hidden; display: flex; flex-direction: column; position: relative; margin: auto;">
            <div class="modal-header" style="flex-shrink: 0; padding: 12px 20px; border-bottom: 2px solid #666666; display: flex; justify-content: space-between; align-items: center; background-color: #000000;">
                <div class="modal-title" style="font-size: 40px; font-weight: 900; color: #00ff00;">CHECK OUT</div>
                <button class="close-btn" onclick="closeCheckoutModal()" style="background-color: #000000; color: #ff0000; border: 2px solid #ff0000; padding: 8px 16px; cursor: pointer; font-size: 24px; font-weight: bold; border-radius: 4px;">×</button>
            </div>

            <div style="flex: 1; display: flex; padding: 10px; gap: 10px; overflow: hidden;">
                <!-- Left Panel -->
                <div style="flex: 1; display: flex; flex-direction: column; gap: 10px; min-width: 0;">
                    <!-- Payment Input Field -->
                    <div style="background-color: #000000; border: 2px solid #00ff00; border-radius: 8px; padding: 10px;">
                        <div style="color: #00ff00; font-size: 18px; font-weight: bold; margin-bottom: 8px; text-align: center;">PAYMENT AMOUNT</div>
                        <input type="text" id="payment-input" readonly style="background-color: #1a1a1a; color: #00ff00; border: 2px solid #00ff00; padding: 12px; border-radius: 6px; font-size: 28px; font-weight: bold; width: 100%; text-align: center;" value="0.00">
                    </div>

                    <!-- Keypad -->
                    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 8px; max-width: 350px;">
                        <button onclick="addToPaymentAmount('1')" style="background-color: #000000; color: #00ff00; border: 2px solid #00ff00; font-size: 36px; font-weight: 900; padding: 18px; cursor: pointer; border-radius: 8px; aspect-ratio: 1; transition: all 0.2s;">1</button>
                        <button onclick="addToPaymentAmount('2')" style="background-color: #000000; color: #00ff00; border: 2px solid #00ff00; font-size: 36px; font-weight: 900; padding: 18px; cursor: pointer; border-radius: 8px; aspect-ratio: 1; transition: all 0.2s;">2</button>
                        <button onclick="addToPaymentAmount('3')" style="background-color: #000000; color: #00ff00; border: 2px solid #00ff00; font-size: 36px; font-weight: 900; padding: 18px; cursor: pointer; border-radius: 8px; aspect-ratio: 1; transition: all 0.2s;">3</button>
                        <button onclick="addToPaymentAmount('4')" style="background-color: #000000; color: #00ff00; border: 2px solid #00ff00; font-size: 36px; font-weight: 900; padding: 18px; cursor: pointer; border-radius: 8px; aspect-ratio: 1; transition: all 0.2s;">4</button>
                        <button onclick="addToPaymentAmount('5')" style="background-color: #000000; color: #00ff00; border: 2px solid #00ff00; font-size: 36px; font-weight: 900; padding: 18px; cursor: pointer; border-radius: 8px; aspect-ratio: 1; transition: all 0.2s;">5</button>
                        <button onclick="addToPaymentAmount('6')" style="background-color: #000000; color: #00ff00; border: 2px solid #00ff00; font-size: 36px; font-weight: 900; padding: 18px; cursor: pointer; border-radius: 8px; aspect-ratio: 1; transition: all 0.2s;">6</button>
                        <button onclick="addToPaymentAmount('7')" style="background-color: #000000; color: #00ff00; border: 2px solid #00ff00; font-size: 36px; font-weight: 900; padding: 18px; cursor: pointer; border-radius: 8px; aspect-ratio: 1; transition: all 0.2s;">7</button>
                        <button onclick="addToPaymentAmount('8')" style="background-color: #000000; color: #00ff00; border: 2px solid #00ff00; font-size: 36px; font-weight: 900; padding: 18px; cursor: pointer; border-radius: 8px; aspect-ratio: 1; transition: all 0.2s;">8</button>
                        <button onclick="addToPaymentAmount('9')" style="background-color: #000000; color: #00ff00; border: 2px solid #00ff00; font-size: 36px; font-weight: 900; padding: 18px; cursor: pointer; border-radius: 8px; aspect-ratio: 1; transition: all 0.2s;">9</button>
                        <button onclick="addToPaymentAmount('.')" style="background-color: #000000; color: #00ff00; border: 2px solid #00ff00; font-size: 36px; font-weight: 900; padding: 18px; cursor: pointer; border-radius: 8px; aspect-ratio: 1; transition: all 0.2s;">.</button>
                        <button onclick="addToPaymentAmount('0')" style="background-color: #000000; color: #00ff00; border: 2px solid #00ff00; font-size: 36px; font-weight: 900; padding: 18px; cursor: pointer; border-radius: 8px; aspect-ratio: 1; transition: all 0.2s;">0</button>
                        <button onclick="clearPaymentAmount()" style="background-color: #aa0000; color: #ffffff; border: 2px solid #ff0000; font-size: 24px; font-weight: 900; padding: 18px; cursor: pointer; border-radius: 8px; aspect-ratio: 1; transition: all 0.2s;">←</button>
                    </div>
                </div>

                <!-- Payment Method Section (Horizontal Layout) -->
                <div style="background-color: #000000; border: 2px solid #00ff00; border-radius: 8px; padding: 12px;">
                    <div style="color: #00ff00; font-size: 20px; font-weight: bold; margin-bottom: 10px; text-align: center;">PAYMENT METHODS</div>

                    <!-- Payment Method Buttons in Horizontal Layout -->
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 10px;">
                        <div style="display: flex; align-items: center; background-color: #1a1a1a; border: 2px solid #666; border-radius: 8px; padding: 10px; cursor: pointer; transition: all 0.2s;" onclick="selectPaymentMethod('Cash')" id="cash-payment-row">
                            <div style="width: 40px; height: 40px; background-color: #00ff00; border-radius: 6px; display: flex; align-items: center; justify-content: center; margin-right: 10px; color: #000000; font-weight: bold; font-size: 20px;">$</div>
                            <div style="flex: 1;">
                                <div style="color: #00ff00; font-weight: bold; font-size: 20px;">CASH →</div>
                            </div>
                            <div style="color: #00ff00; font-weight: 900; font-size: 22px;" id="cash-display">${formatCurrency(calculateTotal())}</div>
                            <button onclick="event.stopPropagation(); clearPaymentMethod('Cash')" style="background-color: #ff0000; color: #ffffff; border: none; padding: 6px 10px; margin-left: 8px; cursor: pointer; border-radius: 4px; font-size: 12px; font-weight: bold;">CLEAR</button>
                        </div>

                        <div style="display: flex; align-items: center; background-color: #1a1a1a; border: 2px solid #666; border-radius: 8px; padding: 10px; cursor: pointer; transition: all 0.2s;" onclick="selectPaymentMethod('Debit')" id="debit-payment-row">
                            <div style="width: 40px; height: 40px; background-color: #00ff00; border-radius: 6px; display: flex; align-items: center; justify-content: center; margin-right: 10px; color: #000000; font-weight: bold; font-size: 14px;">DB</div>
                            <div style="flex: 1;">
                                <div style="color: #00ff00; font-weight: bold; font-size: 20px;">DEBIT →</div>
                            </div>
                            <div style="color: #00ff00; font-weight: 900; font-size: 22px;" id="debit-display">0.00</div>
                            <button onclick="event.stopPropagation(); clearPaymentMethod('Debit')" style="background-color: #ff0000; color: #ffffff; border: none; padding: 6px 10px; margin-left: 8px; cursor: pointer; border-radius: 4px; font-size: 12px; font-weight: bold;">CLEAR</button>
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 10px;">
                        <div style="display: flex; align-items: center; background-color: #1a1a1a; border: 2px solid #666; border-radius: 8px; padding: 10px; cursor: pointer; transition: all 0.2s;" onclick="selectPaymentMethod('Credit')" id="credit-payment-row">
                            <div style="width: 40px; height: 40px; background-color: #00ff00; border-radius: 6px; display: flex; align-items: center; justify-content: center; margin-right: 10px; color: #000000; font-weight: bold; font-size: 14px;">CR</div>
                            <div style="flex: 1;">
                                <div style="color: #00ff00; font-weight: bold; font-size: 20px;">CREDIT →</div>
                            </div>
                            <div style="color: #00ff00; font-weight: 900; font-size: 22px;" id="credit-display">0.00</div>
                            <button onclick="event.stopPropagation(); clearPaymentMethod('Credit')" style="background-color: #ff0000; color: #ffffff; border: none; padding: 6px 10px; margin-left: 8px; cursor: pointer; border-radius: 4px; font-size: 12px; font-weight: bold;">CLEAR</button>
                        </div>

                        <div style="display: flex; align-items: center; background-color: #1a1a1a; border: 2px solid #666; border-radius: 8px; padding: 10px;">
                            <div style="flex: 1;">
                                <div style="color: #ff0000; font-weight: bold; font-size: 20px;">CARD NO →</div>
                            </div>
                            <input type="text" placeholder="Enter card number" style="background-color: #000000; color: #00ff00; border: 2px solid #00ff00; padding: 8px; border-radius: 4px; font-size: 16px; width: 150px;" id="card-number-input">
                        </div>
                    </div>

                    <button onclick="clearAllPayments()" style="background-color: #aa0000; color: #ffffff; border: 2px solid #ff0000; font-size: 18px; font-weight: bold; padding: 12px; cursor: pointer; border-radius: 8px; width: 100%; transition: all 0.2s;">CLEAR CHANGE</button>

                    <!-- Product Display Area (Below Clear Change Button) -->
                    <div style="margin-top: 10px;">
                        <div style="color: #00ff00; font-size: 18px; font-weight: bold; margin-bottom: 8px; text-align: center;">ITEMS</div>
                        <div style="background-color: #1a1a1a; border: 2px solid #00ff00; border-radius: 6px; padding: 10px; height: 120px; overflow-y: auto;">
                            ${productListHTML}
                        </div>
                    </div>
                </div>

                <!-- Right Panel -->
                <div style="width: 320px; display: flex; flex-direction: column; gap: 10px;">
                    <!-- Item Count -->
                    <div style="background-color: #000000; border: 2px solid #00ff00; border-radius: 8px; padding: 15px; text-align: center;">
                        <div style="color: #00ff00; font-size: 22px; font-weight: bold; margin-bottom: 8px;">Item Count</div>
                        <div style="background-color: #1a1a1a; border: 2px solid #00ff00; border-radius: 6px; padding: 15px;">
                            <div style="color: #00ff00; font-size: 56px; font-weight: 900;">${cartItems.length}</div>
                        </div>
                    </div>

                    <!-- Net Total -->
                    <div style="background-color: #000000; border: 2px solid #00ff00; border-radius: 8px; padding: 15px; text-align: center;">
                        <div style="color: #00ff00; font-size: 22px; font-weight: bold; margin-bottom: 8px;">Net Total</div>
                        <div style="background-color: #1a1a1a; border: 2px solid #00ff00; border-radius: 6px; padding: 15px;">
                            <div style="color: #00ff00; font-size: 56px; font-weight: 900;">${formatCurrency(calculateTotal())}</div>
                        </div>
                    </div>

                    <!-- Change -->
                    <div style="background-color: #000000; border: 2px solid #00ff00; border-radius: 8px; padding: 15px; text-align: center;">
                        <div style="color: #00ff00; font-size: 22px; font-weight: bold; margin-bottom: 8px;">Change</div>
                        <div style="background-color: #1a1a1a; border: 2px solid #00ff00; border-radius: 6px; padding: 15px;">
                            <div style="color: #00ff00; font-size: 56px; font-weight: 900;" id="change-amount-display">0.00</div>
                        </div>
                    </div>

                    <!-- Close Bill Button -->
                    <button onclick="confirmCheckout()" style="background-color: #aa0000; color: #ffffff; border: 2px solid #ff0000; font-size: 26px; font-weight: 900; padding: 18px; cursor: pointer; border-radius: 8px; margin-top: 10px; transition: all 0.2s;">
                        CLOSE BILL
                    </button>
                </div>
            </div>

            <!-- Bottom Close Button -->
            <div style="flex-shrink: 0; padding: 16px; border-top: 2px solid #666666; text-align: center;">
                <button onclick="closeCheckoutModal()" style="background-color: #000000; color: #ff0000; border: 2px solid #ff0000; font-size: 20px; font-weight: 900; padding: 12px 40px; cursor: pointer; border-radius: 8px;">
                    ✗ CLOSE
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Initialize payment amounts and update displays
    updatePaymentDisplays();
}

// Function to add digits to payment amount
function addToPaymentAmount(digit) {
    if (digit === '.' && currentPaymentAmount.includes('.')) {
        return; // Don't allow multiple decimal points
    }

    currentPaymentAmount += digit;

    // Update the input field display
    const paymentInput = document.getElementById('payment-input');
    if (paymentInput) {
        paymentInput.value = currentPaymentAmount || '0.00';
    }

    // Update the selected payment method amount
    if (selectedPaymentMethod && selectedPaymentMethod !== 'Other') {
        const amount = parseFloat(currentPaymentAmount) || 0;
        paymentAmounts[selectedPaymentMethod] = amount;
        updatePaymentDisplays();
    }
}

// Function to clear payment amount input
function clearPaymentAmount() {
    currentPaymentAmount = currentPaymentAmount.slice(0, -1);

    // Update the input field display
    const paymentInput = document.getElementById('payment-input');
    if (paymentInput) {
        paymentInput.value = currentPaymentAmount || '0.00';
    }

    // Update the selected payment method amount
    if (selectedPaymentMethod && selectedPaymentMethod !== 'Other') {
        const amount = parseFloat(currentPaymentAmount) || 0;
        paymentAmounts[selectedPaymentMethod] = amount;
        updatePaymentDisplays();
    }
}

// Function to select payment method
function selectPaymentMethod(method) {
    selectedPaymentMethod = method;

    // Reset current input when switching methods
    currentPaymentAmount = '';

    // Update the input field display
    const paymentInput = document.getElementById('payment-input');
    if (paymentInput) {
        paymentInput.value = '0.00';
    }

    // Update visual selection
    updatePaymentMethodSelection();
}

// Function to clear specific payment method
function clearPaymentMethod(method) {
    paymentAmounts[method] = 0;
    if (selectedPaymentMethod === method) {
        currentPaymentAmount = '';
        // Update the input field display
        const paymentInput = document.getElementById('payment-input');
        if (paymentInput) {
            paymentInput.value = '0.00';
        }
    }
    updatePaymentDisplays();
}

// Function to clear all payments
function clearAllPayments() {
    paymentAmounts = {
        Cash: 0,
        Debit: 0,
        Credit: 0
    };
    currentPaymentAmount = '';

    // Update the input field display
    const paymentInput = document.getElementById('payment-input');
    if (paymentInput) {
        paymentInput.value = '0.00';
    }

    updatePaymentDisplays();
}

// Function to update payment displays
function updatePaymentDisplays() {
    const cashDisplay = document.getElementById('cash-display');
    const debitDisplay = document.getElementById('debit-display');
    const creditDisplay = document.getElementById('credit-display');
    const changeDisplay = document.getElementById('change-amount-display');

    if (cashDisplay) cashDisplay.textContent = formatCurrency(paymentAmounts.Cash);
    if (debitDisplay) debitDisplay.textContent = formatCurrency(paymentAmounts.Debit);
    if (creditDisplay) creditDisplay.textContent = formatCurrency(paymentAmounts.Credit);

    // Calculate change
    const totalPaid = paymentAmounts.Cash + paymentAmounts.Debit + paymentAmounts.Credit;
    const totalDue = calculateTotal();
    const change = Math.max(0, totalPaid - totalDue);

    if (changeDisplay) changeDisplay.textContent = formatCurrency(change);
}

// Function to update payment method selection visual
function updatePaymentMethodSelection() {
    const cashRow = document.getElementById('cash-payment-row');
    const debitRow = document.getElementById('debit-payment-row');
    const creditRow = document.getElementById('credit-payment-row');

    // Reset all styles to default (unselected state)
    if (cashRow) {
        cashRow.style.backgroundColor = '#1a1a1a';
        cashRow.style.borderColor = '#666';
        // Update text colors for cash (make them green like other methods)
        const cashText = cashRow.querySelector('div:nth-child(2) div');
        const cashAmount = cashRow.querySelector('div:nth-child(3)');
        if (cashText) cashText.style.color = '#00ff00';
        if (cashAmount) cashAmount.style.color = '#00ff00';
    }
    if (debitRow) {
        debitRow.style.backgroundColor = '#1a1a1a';
        debitRow.style.borderColor = '#666';
    }
    if (creditRow) {
        creditRow.style.backgroundColor = '#1a1a1a';
        creditRow.style.borderColor = '#666';
    }

    // Highlight selected method
    if (selectedPaymentMethod === 'Cash' && cashRow) {
        cashRow.style.backgroundColor = '#006600';
        cashRow.style.borderColor = '#00ff00';
        // Update text colors for selected cash (make them black)
        const cashText = cashRow.querySelector('div:nth-child(2) div');
        const cashAmount = cashRow.querySelector('div:nth-child(3)');
        if (cashText) cashText.style.color = '#000000';
        if (cashAmount) cashAmount.style.color = '#000000';
    } else if (selectedPaymentMethod === 'Debit' && debitRow) {
        debitRow.style.backgroundColor = '#006600';
        debitRow.style.borderColor = '#00ff00';
    } else if (selectedPaymentMethod === 'Credit' && creditRow) {
        creditRow.style.backgroundColor = '#006600';
        creditRow.style.borderColor = '#00ff00';
    }
}



// Confirm checkout and show success modal
async function confirmCheckout() {
    try {
        // Prepare sale data
        const saleData = {
            subtotal: calculateSubtotal(),
            taxAmount: calculateTax(),
            discountAmount: discountAmount,
            totalAmount: calculateTotal(),
            paymentCash: paymentAmounts.Cash || 0,
            paymentDebit: paymentAmounts.Debit || 0,
            paymentCredit: paymentAmounts.Credit || 0,
            paymentTotal: (paymentAmounts.Cash || 0) + (paymentAmounts.Debit || 0) + (paymentAmounts.Credit || 0),
            changeAmount: Math.max(0, ((paymentAmounts.Cash || 0) + (paymentAmounts.Debit || 0) + (paymentAmounts.Credit || 0)) - calculateTotal()),
            itemCount: cartItems.length,
            items: cartItems.map(item => ({
                productId: item.productId ? parseInt(item.productId) : null,
                name: item.description,
                barcode: item.barcode || '',
                category: item.category || '',
                subcategory: item.subcategory || '',
                quantity: item.quantity,
                price: item.price,
                total: item.value,
                isDeliItem: item.isDeliItem || false
            }))
        };

        console.log('POS - 💰 CHECKOUT: Saving sale data:', {
            total: saleData.totalAmount,
            items: saleData.itemCount,
            payments: {
                cash: saleData.paymentCash,
                debit: saleData.paymentDebit,
                credit: saleData.paymentCredit
            }
        });

        // Debug deli items
        const deliItems = saleData.items.filter(item => item.isDeliItem);
        console.log('🔍 DELI DEBUG - POS Checkout:');
        console.log(`  Total items: ${saleData.items.length}`);
        console.log(`  Deli items: ${deliItems.length}`);
        if (deliItems.length > 0) {
            console.log(`  Deli item details:`, deliItems);
        }

        // Save sale to database
        const result = await ipcRenderer.invoke('create-sale', saleData);

        if (result.success) {
            console.log(`POS - ✅ Sale saved successfully: ${result.data.saleId}`);

            // Open cash drawer automatically
            if (cashDrawer) {
                try {
                    console.log('💰 Opening cash drawer...');
                    const drawerResult = await cashDrawer.openDrawer();

                    if (drawerResult.opened) {
                        console.log(`✅ Cash drawer opened successfully (${drawerResult.method})`);
                    } else if (drawerResult.success && !drawerResult.opened) {
                        console.log('ℹ️ Cash drawer not connected - continuing without error');
                    } else {
                        console.warn('⚠️ Cash drawer failed to open:', drawerResult.error);
                    }
                } catch (error) {
                    console.error('❌ Cash drawer error:', error);
                    // Don't stop the checkout process for cash drawer errors
                }
            } else {
                console.log('ℹ️ Cash drawer service not initialized');
            }

            // Update customer display with sale completion
            if (customerDisplay && customerDisplay.isConnected) {
                customerDisplay.showCheckoutComplete(saleData);
            }

            // Automatically print receipt
            try {
                console.log('🖨️ Attempting to print receipt automatically...');
                const printResult = await ipcRenderer.invoke('print-receipt', result.data);

                if (printResult.success) {
                    console.log('✅ Receipt printed successfully:', printResult.printer);
                } else {
                    console.warn('⚠️ Receipt printing failed:', printResult.message);
                    // Don't block the checkout process if printing fails
                }
            } catch (printError) {
                console.error('❌ Receipt printing error:', printError);
                // Don't block the checkout process if printing fails
            }

            // Store cart data for success modal display
            const completedSaleData = {
                itemCount: cartItems.length,
                subtotal: calculateSubtotal(),
                tax: calculateTax(),
                total: calculateTotal(),
                paymentMethod: selectedPaymentMethod
            };

            // Reset cart immediately after successful sale
            resetCart();

            closeCheckoutModal();
            showSuccessModal(result.data.saleId, result.data, completedSaleData);
        } else {
            console.error('POS - Failed to save sale:', result.message);
            alert(`Failed to save sale: ${result.message}`);
        }
    } catch (error) {
        console.error('POS - Error during checkout:', error);
        alert('An error occurred during checkout. Please try again.');
    }
}

// Show final success modal
function showSuccessModal(saleId = null, saleData = null, completedSaleData = null) {
    const modal = document.createElement('div');
    modal.className = 'modal show';
    modal.id = 'success-modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">SUCCESSFUL SALE!</div>
                <button class="close-btn" onclick="closeCheckoutModal()">×</button>
            </div>
            <div style="text-align: center; padding: 32px; display: flex; flex-direction: column; gap: 32px;">
                <div style="background-color: #1a1a1a; border: 2px solid #00ff00; border-radius: 8px; padding: 32px;">
                    <div style="font-size: 96px; font-weight: 900; color: #00ff00; line-height: 1;">$${formatCurrency(completedSaleData?.total || 0)}</div>
                    ${saleId ? `<div style="font-size: 18px; color: #00ff00; margin-top: 16px; opacity: 0.8;">Sale ID: ${saleId}</div>` : ''}
                </div>

                <div style="background-color: #0a0a0a; border: 2px solid #00ff00; border-radius: 8px; padding: 20px; margin: 16px 0;">
                    <div style="font-size: 24px; font-weight: bold; color: #00ff00; margin-bottom: 12px;">Payment Method: ${completedSaleData?.paymentMethod || 'Cash'}</div>
                    <div style="font-size: 18px; color: #00cc00;">Transaction completed successfully!</div>
                    <div style="font-size: 14px; color: #00aa00; margin-top: 8px;">Cart has been automatically reset for next transaction</div>
                </div>

                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 24px;">
                    <div style="background-color: #1a1a1a; border: 1px solid #00ff00; border-radius: 8px; padding: 24px;">
                        <div style="font-size: 24px; font-weight: bold; color: #00cc00; margin-bottom: 8px;">Items Sold</div>
                        <div style="font-size: 48px; font-weight: 900; color: #00ff00;">${completedSaleData?.itemCount || 0}</div>
                    </div>
                    <div style="background-color: #1a1a1a; border: 1px solid #00ff00; border-radius: 8px; padding: 24px;">
                        <div style="font-size: 24px; font-weight: bold; color: #00cc00; margin-bottom: 8px;">Subtotal</div>
                        <div style="font-size: 48px; font-weight: 900; color: #00ff00;">$${formatCurrency(completedSaleData?.subtotal || 0)}</div>
                    </div>
                    <div style="background-color: #1a1a1a; border: 1px solid #00ff00; border-radius: 8px; padding: 24px;">
                        <div style="font-size: 24px; font-weight: bold; color: #00cc00; margin-bottom: 8px;">Tax (6.625%)</div>
                        <div style="font-size: 48px; font-weight: 900; color: #00ff00;">$${formatCurrency(completedSaleData?.tax || 0)}</div>
                    </div>
                </div>

                <div style="display: flex; gap: 16px; justify-content: center; margin-top: 48px; flex-wrap: wrap;">
                    <button onclick="closeCheckoutModal()" style="background-color: #00aa00; color: #000000; border: 2px solid #00ff00; font-size: 28px; font-weight: 900; padding: 20px 40px; cursor: pointer; border-radius: 8px;">
                        CONTINUE (Auto-close in <span id="auto-close-timer">10</span>s)
                    </button>
                    ${saleData ? `<button onclick="reprintReceipt('${saleId}')" style="background-color: #0066cc; color: #ffffff; border: 2px solid #0088ff; font-size: 28px; font-weight: 900; padding: 20px 40px; cursor: pointer; border-radius: 8px;">
                        🖨️ REPRINT RECEIPT
                    </button>` : ''}
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Start auto-close timer
    let timeLeft = 10;
    const timerElement = document.getElementById('auto-close-timer');

    const countdown = setInterval(() => {
        timeLeft--;
        if (timerElement) {
            timerElement.textContent = timeLeft;
        }

        if (timeLeft <= 0) {
            clearInterval(countdown);
            closeCheckoutModal();
        }
    }, 1000);

    // Store sale data globally for reprint functionality
    if (saleData) {
        window.lastSaleData = saleData;
    }
}

// Reprint receipt function
async function reprintReceipt(saleId) {
    try {
        console.log('🖨️ Reprinting receipt for sale:', saleId);

        // Use stored sale data or fetch from database if needed
        let saleData = window.lastSaleData;

        if (!saleData) {
            // If no stored data, we could fetch from database here
            console.warn('⚠️ No sale data available for reprint');
            alert('Sale data not available for reprint. Please try again.');
            return;
        }

        const printResult = await ipcRenderer.invoke('print-receipt', saleData);

        if (printResult.success) {
            console.log('✅ Receipt reprinted successfully:', printResult.printer);
            alert(`✅ Receipt reprinted successfully!\nPrinter: ${printResult.printer}`);
        } else {
            console.error('❌ Reprint failed:', printResult.message);
            alert(`❌ Failed to reprint receipt:\n\n${printResult.message}`);
        }

    } catch (error) {
        console.error('❌ Reprint error:', error);
        alert(`❌ Error reprinting receipt:\n\n${error.message}`);
    }
}

function closeCheckoutModal() {
    const modal = document.querySelector('.modal');
    if (modal) {
        modal.remove();
    }
}

function completeCheckout() {
    // Use the centralized reset function
    resetCart();
    closeCheckoutModal();
}

// Draft Sales Functions
async function handleHold() {
    if (cartItems.length === 0) {
        alert('No items in cart to hold');
        return;
    }

    try {
        // Prepare draft sale data
        const draftData = {
            itemCount: cartItems.length,
            totalAmount: calculateTotal(),
            items: cartItems.map(item => ({
                productId: item.productId ? parseInt(item.productId) : null,
                name: item.description,
                barcode: item.barcode || '',
                category: item.category || '',
                subcategory: item.subcategory || '',
                quantity: item.quantity,
                price: item.price,
                total: item.value,
                discount: item.discount || 0
            }))
        };

        console.log('POS - 📋 HOLD: Saving draft sale:', {
            items: draftData.itemCount,
            total: draftData.totalAmount
        });

        // Save draft sale to database
        const result = await ipcRenderer.invoke('create-draft-sale', draftData);

        if (result.success) {
            console.log(`POS - ✅ Draft sale saved: ${result.data.draftSaleId}`);

            // Clear current cart using centralized function
            resetCart();

            alert(`Cart saved as draft: ${result.data.draftSaleId}`);
        } else {
            console.error('POS - Failed to save draft sale:', result.message);
            alert(`Failed to save draft: ${result.message}`);
        }
    } catch (error) {
        console.error('POS - Error saving draft sale:', error);
        alert('An error occurred while saving draft. Please try again.');
    }
}

async function handleRecall() {
    try {
        console.log('POS - 📋 RECALL: Loading draft sales...');

        // Get draft sales from database
        const result = await ipcRenderer.invoke('get-draft-sales');

        if (result.success) {
            if (result.drafts.length === 0) {
                alert('No draft sales found');
                return;
            }

            console.log(`POS - Found ${result.drafts.length} draft sales`);
            showDraftSalesModal(result.drafts);
        } else {
            console.error('POS - Failed to load draft sales:', result.message);
            alert(`Failed to load drafts: ${result.message}`);
        }
    } catch (error) {
        console.error('POS - Error loading draft sales:', error);
        alert('An error occurred while loading drafts. Please try again.');
    }
}

function showDraftSalesModal(drafts) {
    const modal = document.createElement('div');
    modal.className = 'modal show';
    modal.id = 'draft-sales-modal';

    modal.innerHTML = `
        <div class="modal-content" style="background-color: #000000; border: 2px solid #00ff00; color: #00ff00; width: 90vw; height: 85vh; max-width: 1200px; overflow: hidden; display: flex; flex-direction: column;">
            <div class="modal-header" style="flex-shrink: 0; padding: 16px 24px; border-bottom: 2px solid #666666; display: flex; justify-content: space-between; align-items: center;">
                <div class="modal-title" style="font-size: 32px; font-weight: 900; color: #00ff00;">RECALL DRAFT SALES</div>
                <button class="close-btn" onclick="closeDraftSalesModal()" style="background: none; border: none; color: #ff0000; font-size: 32px; cursor: pointer; padding: 0; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">×</button>
            </div>

            <div style="flex: 1; padding: 20px; overflow-y: auto;">
                <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 16px;">
                    ${drafts.map(draft => `
                        <div class="draft-card" onclick="selectDraftSale('${draft.draft_sale_id}')" style="
                            background-color: #1a1a1a;
                            border: 2px solid #00ff00;
                            border-radius: 8px;
                            padding: 16px;
                            cursor: pointer;
                            transition: all 0.3s ease;
                            hover: background-color: #2a2a2a;
                        " onmouseover="this.style.backgroundColor='#2a2a2a'" onmouseout="this.style.backgroundColor='#1a1a1a'">
                            <div style="font-size: 18px; font-weight: bold; color: #00ff00; margin-bottom: 8px;">
                                ${draft.draft_sale_id}
                            </div>
                            <div style="font-size: 14px; color: #cccccc; margin-bottom: 4px;">
                                Items: ${draft.item_count}
                            </div>
                            <div style="font-size: 16px; font-weight: bold; color: #00ff00; margin-bottom: 8px;">
                                Total: $${parseFloat(draft.total_amount).toFixed(2)}
                            </div>
                            <div style="font-size: 12px; color: #888888;">
                                Created: ${new Date(draft.created_at).toLocaleString()}
                            </div>
                            <div style="font-size: 12px; color: #888888;">
                                By: ${draft.operator_name}
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>

            <div style="flex-shrink: 0; padding: 16px; border-top: 2px solid #666666; text-align: center;">
                <button onclick="closeDraftSalesModal()" style="background-color: #aa0000; color: #ffffff; border: 2px solid #ff0000; font-size: 18px; font-weight: 900; padding: 12px 32px; cursor: pointer; border-radius: 8px;">
                    CLOSE
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
}

function closeDraftSalesModal() {
    const modal = document.getElementById('draft-sales-modal');
    if (modal) {
        modal.remove();
    }
}

async function selectDraftSale(draftSaleId) {
    try {
        console.log(`POS - 📋 RECALL: Loading draft sale ${draftSaleId}...`);

        // Get draft sale details
        const result = await ipcRenderer.invoke('get-draft-sale-details', draftSaleId);

        if (result.success && result.draft) {
            const draft = result.draft;

            // Clear current cart using centralized function
            resetCart();

            // Load draft items into cart
            draft.items.forEach(item => {
                const cartItem = {
                    id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
                    qh: cartItems.length,
                    description: item.product_name,
                    quantity: item.quantity,
                    price: parseFloat(item.unit_price),
                    discount: parseFloat(item.discount) || 0,
                    value: parseFloat(item.total_price),
                    // Store additional product details
                    productId: item.product_id,
                    barcode: item.product_barcode || '',
                    category: item.product_category || '',
                    subcategory: item.product_subcategory || ''
                };
                cartItems.push(cartItem);
            });

            // Update displays
            updateCartDisplay();
            updateTotals();
            updateDisplay();

            // Close modal
            closeDraftSalesModal();

            console.log(`POS - ✅ Draft sale ${draftSaleId} recalled with ${draft.items.length} items`);
            alert(`Draft sale recalled: ${draftSaleId}\nItems loaded: ${draft.items.length}`);

            // Optionally delete the draft sale after successful recall
            if (confirm('Delete this draft sale now that it has been recalled?')) {
                const deleteResult = await ipcRenderer.invoke('delete-draft-sale', draftSaleId);
                if (deleteResult.success) {
                    console.log(`POS - ✅ Draft sale ${draftSaleId} deleted after recall`);
                }
            }
        } else {
            console.error('POS - Failed to load draft sale details:', result.message);
            alert(`Failed to load draft sale: ${result.message}`);
        }
    } catch (error) {
        console.error('POS - Error recalling draft sale:', error);
        alert('An error occurred while recalling draft sale. Please try again.');
    }
}

// Draft Sales Functions
async function handleHold() {
    if (cartItems.length === 0) {
        alert('No items in cart to hold');
        return;
    }

    try {
        // Prepare draft sale data
        const draftData = {
            itemCount: cartItems.length,
            totalAmount: calculateTotal(),
            items: cartItems.map(item => ({
                productId: item.productId ? parseInt(item.productId) : null,
                name: item.description,
                barcode: item.barcode || '',
                category: item.category || '',
                subcategory: item.subcategory || '',
                quantity: item.quantity,
                price: item.price,
                total: item.value,
                discount: item.discount || 0
            }))
        };

        console.log('POS - 📋 HOLD: Saving draft sale:', {
            items: draftData.itemCount,
            total: draftData.totalAmount
        });

        // Save draft sale to database
        const result = await ipcRenderer.invoke('create-draft-sale', draftData);

        if (result.success) {
            console.log(`POS - ✅ Draft sale saved: ${result.data.draftSaleId}`);

            // Clear current cart using centralized function
            resetCart();

            alert(`Cart saved as draft: ${result.data.draftSaleId}`);
        } else {
            console.error('POS - Failed to save draft sale:', result.message);
            alert(`Failed to save draft: ${result.message}`);
        }
    } catch (error) {
        console.error('POS - Error saving draft sale:', error);
        alert('An error occurred while saving draft. Please try again.');
    }
}

async function handleRecall() {
    try {
        console.log('POS - 📋 RECALL: Loading draft sales...');

        // Get draft sales from database
        const result = await ipcRenderer.invoke('get-draft-sales');

        if (result.success) {
            if (result.drafts.length === 0) {
                alert('No draft sales found');
                return;
            }

            console.log(`POS - Found ${result.drafts.length} draft sales`);
            showDraftSalesModal(result.drafts);
        } else {
            console.error('POS - Failed to load draft sales:', result.message);
            alert(`Failed to load drafts: ${result.message}`);
        }
    } catch (error) {
        console.error('POS - Error loading draft sales:', error);
        alert('An error occurred while loading drafts. Please try again.');
    }
}

// Product modal functions
let selectedCategory = null;
let selectedSubcategory = null;
let sortOrder = 'asc'; // 'asc' or 'desc'
let priceFilter = 'all'; // 'all', 'low-to-high', 'high-to-low'

async function showProductModal() {
    console.log('POS - showProductModal called');
    selectedCategory = null;
    selectedSubcategory = null;
    sortOrder = 'asc';
    priceFilter = 'all';

    // Always refresh products to ensure we have the latest data
    console.log('POS - Loading products from database...');
    await loadProducts();

    console.log(`POS - Opening product modal with ${demoProducts.length} products available`);

    if (demoProducts.length === 0) {
        console.error('POS - No products loaded from database!');
        alert('No products found in database. Please add products first.');
        return;
    }

    const modal = document.createElement('div');
    modal.className = 'modal show';
    modal.id = 'product-modal';
    modal.innerHTML = `
        <div style="background-color: #ffffff; border: none; border-radius: 16px; color: #333333; width: 95vw; height: 90vh; max-width: 1400px; overflow: hidden; display: flex; flex-direction: column; position: relative; margin: auto; box-shadow: 0 20px 60px rgba(0,0,0,0.3);">
            <!-- Header -->
            <div style="flex-shrink: 0; padding: 24px 32px; border-bottom: 1px solid #e5e7eb; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <h2 style="font-size: 32px; font-weight: 700; margin: 0; color: white;">Select Products</h2>
                    <div style="display: flex; gap: 12px; align-items: center;">
                        <button onclick="refreshProductsInModal()" style="background: rgba(255,255,255,0.2); color: white; border: none; padding: 8px 16px; border-radius: 25px; cursor: pointer; font-size: 14px; font-weight: 500; transition: all 0.3s ease;" onmouseover="this.style.background='rgba(255,255,255,0.3)'" onmouseout="this.style.background='rgba(255,255,255,0.2)'">🔄 Refresh</button>
                        <button onclick="closeProductModal()" style="background: rgba(255,255,255,0.2); color: white; border: none; width: 48px; height: 48px; border-radius: 50%; cursor: pointer; font-size: 24px; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease;" onmouseover="this.style.background='rgba(255,255,255,0.3)'" onmouseout="this.style.background='rgba(255,255,255,0.2)'">×</button>
                    </div>
                </div>
            </div>

            <!-- Category Filter Bar -->
            <div style="flex-shrink: 0; padding: 20px 32px; background-color: #f8fafc; border-bottom: 1px solid #e5e7eb;">
                <div style="display: flex; gap: 12px; align-items: center; flex-wrap: wrap;">
                    <span style="font-weight: 600; color: #374151; margin-right: 8px;">Categories:</span>
                    <button onclick="selectCategory(null)" id="category-all" style="padding: 8px 16px; border: 2px solid #667eea; background: #667eea; color: #000000; border-radius: 25px; cursor: pointer; font-weight: 700; transition: all 0.3s ease;">All Products</button>
                    ${[...new Set(demoProducts.map(p => p.category))].map(category => `
                        <button onclick="selectCategory('${category}')" id="category-${category.replace(/[^a-zA-Z0-9]/g, '')}" style="padding: 8px 16px; border: 2px solid #e5e7eb; background: #f8f9fa; color: #000000; border-radius: 25px; cursor: pointer; font-weight: 600; transition: all 0.3s ease;">${category}</button>
                    `).join('')}
                </div>
            </div>

            <!-- Subcategory Filter Bar -->
            <div id="subcategory-filter-bar" style="flex-shrink: 0; padding: 15px 32px; background-color: #f1f5f9; border-bottom: 1px solid #e5e7eb; display: none;">
                <div style="display: flex; gap: 12px; align-items: center; flex-wrap: wrap;">
                    <span style="font-weight: 600; color: #374151; margin-right: 8px;">Subcategories:</span>
                    <button onclick="selectSubcategory(null)" id="subcategory-all" style="padding: 6px 14px; border: 2px solid #10b981; background: #10b981; color: #000000; border-radius: 20px; cursor: pointer; font-weight: 700; transition: all 0.3s ease; font-size: 14px;">All Items</button>
                    <div id="subcategories-list" style="display: flex; gap: 8px; flex-wrap: wrap;">
                        <!-- Subcategories will be populated here -->
                    </div>
                </div>
            </div>

            <!-- Sorting and Price Filter Bar -->
            <div style="flex-shrink: 0; padding: 15px 32px; background-color: #e2e8f0; border-bottom: 1px solid #e5e7eb;">
                <div style="display: flex; gap: 20px; align-items: center; flex-wrap: wrap;">
                    <!-- Sorting Controls -->
                    <div style="display: flex; gap: 8px; align-items: center;">
                        <span style="font-weight: 600; color: #374151; margin-right: 8px;">Sort by Barcode:</span>
                        <button onclick="setSortOrder('asc')" id="sort-asc" style="padding: 6px 12px; border: 2px solid #3b82f6; background: #3b82f6; color: #000000; border-radius: 18px; cursor: pointer; font-weight: 700; transition: all 0.3s ease; font-size: 13px;">Ascending ↑</button>
                        <button onclick="setSortOrder('desc')" id="sort-desc" style="padding: 6px 12px; border: 2px solid #e5e7eb; background: #f8f9fa; color: #000000; border-radius: 18px; cursor: pointer; font-weight: 600; transition: all 0.3s ease; font-size: 13px;">Descending ↓</button>
                    </div>

                    <!-- Price Filter Controls -->
                    <div style="display: flex; gap: 8px; align-items: center;">
                        <span style="font-weight: 600; color: #374151; margin-right: 8px;">Price:</span>
                        <button onclick="setPriceFilter('all')" id="price-all" style="padding: 6px 12px; border: 2px solid #10b981; background: #10b981; color: #000000; border-radius: 18px; cursor: pointer; font-weight: 700; transition: all 0.3s ease; font-size: 13px;">All</button>
                        <button onclick="setPriceFilter('low-to-high')" id="price-low-high" style="padding: 6px 12px; border: 2px solid #e5e7eb; background: #f8f9fa; color: #000000; border-radius: 18px; cursor: pointer; font-weight: 600; transition: all 0.3s ease; font-size: 13px;">$ Low → High</button>
                        <button onclick="setPriceFilter('high-to-low')" id="price-high-low" style="padding: 6px 12px; border: 2px solid #e5e7eb; background: #f8f9fa; color: #000000; border-radius: 18px; cursor: pointer; font-weight: 600; transition: all 0.3s ease; font-size: 13px;">$ High → Low</button>
                    </div>
                </div>
            </div>

            <!-- Products Grid -->
            <div style="flex: 1; padding: 24px 32px; overflow-y: auto; background-color: #ffffff;">
                <div id="products-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); gap: 20px;">
                    <!-- Products will be populated here -->
                </div>
                <div id="no-products" style="display: none; text-align: center; padding: 60px 20px; color: #9ca3af;">
                    <div style="font-size: 48px; margin-bottom: 16px;">📦</div>
                    <div style="font-size: 18px; font-weight: 500;">No products found</div>
                    <div style="font-size: 14px; margin-top: 8px;">Try selecting a different category</div>
                </div>
            </div>

            <!-- Footer -->
            <div id="modal-footer" style="flex-shrink: 0; padding: 20px 32px; background-color: #f8fafc; border-top: 1px solid #e5e7eb;">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div style="color: #374151; font-weight: 600; font-size: 16px;">
                        Items in Cart: <span style="color: #667eea; font-size: 20px; font-weight: 700;">${cartItems.length}</span>
                    </div>
                    <div style="color: #374151; font-weight: 600; font-size: 16px;">
                        Total: <span style="color: #10b981; font-size: 24px; font-weight: 700;">$${formatCurrency(calculateTotal())}</span>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Populate the products grid with all available products
    populateProductsGrid();

    // Log the current state for debugging
    console.log(`POS - Product modal opened successfully with ${demoProducts.length} products displayed`);
}

function closeProductModal() {
    const modal = document.getElementById('product-modal');
    if (modal) {
        modal.remove();
    }
}

// Function to refresh products in the modal
async function refreshProductsInModal() {
    console.log('POS - Refreshing products from database...');
    await loadProducts();
    populateProductsGrid();
    console.log(`POS - Products refreshed, now showing ${demoProducts.length} products`);
}

// Sorting and filtering functions
function setSortOrder(order) {
    sortOrder = order;
    priceFilter = 'all'; // Reset price filter when using barcode sort
    console.log(`POS - Sort order set to: ${order} (by barcode), price filter reset to 'all'`);

    // Update barcode sort button styles with black text always visible
    const ascBtn = document.getElementById('sort-asc');
    const descBtn = document.getElementById('sort-desc');

    if (order === 'asc') {
        if (ascBtn) {
            ascBtn.style.background = '#3b82f6';
            ascBtn.style.color = '#000000';
            ascBtn.style.borderColor = '#3b82f6';
            ascBtn.style.fontWeight = '700';
        }

        if (descBtn) {
            descBtn.style.background = '#f8f9fa';
            descBtn.style.color = '#000000';
            descBtn.style.borderColor = '#e5e7eb';
            descBtn.style.fontWeight = '600';
        }
    } else {
        if (descBtn) {
            descBtn.style.background = '#3b82f6';
            descBtn.style.color = '#000000';
            descBtn.style.borderColor = '#3b82f6';
            descBtn.style.fontWeight = '700';
        }

        if (ascBtn) {
            ascBtn.style.background = '#f8f9fa';
            ascBtn.style.color = '#000000';
            ascBtn.style.borderColor = '#e5e7eb';
            ascBtn.style.fontWeight = '600';
        }
    }

    // Reset price filter buttons to default
    resetPriceFilterButtons();

    populateProductsGrid();
}

function setPriceFilter(filter) {
    priceFilter = filter;

    // Reset barcode sort when using price filter (except for 'all')
    if (filter !== 'all') {
        sortOrder = 'asc'; // Reset to default
        resetBarcodeFilterButtons();
    }

    console.log(`POS - Price filter set to: ${filter}, barcode sort reset if needed`);

    // Update button styles with black text always visible
    const allBtn = document.getElementById('price-all');
    const lowHighBtn = document.getElementById('price-low-high');
    const highLowBtn = document.getElementById('price-high-low');

    // Reset all buttons to default state with black text
    [allBtn, lowHighBtn, highLowBtn].forEach(btn => {
        if (btn) {
            btn.style.background = '#f8f9fa';
            btn.style.color = '#000000';
            btn.style.borderColor = '#e5e7eb';
            btn.style.fontWeight = '600';
        }
    });

    // Highlight selected button with black text
    let selectedBtn;
    switch (filter) {
        case 'all':
            selectedBtn = allBtn;
            break;
        case 'low-to-high':
            selectedBtn = lowHighBtn;
            break;
        case 'high-to-low':
            selectedBtn = highLowBtn;
            break;
    }

    if (selectedBtn) {
        selectedBtn.style.background = '#10b981';
        selectedBtn.style.color = '#000000';
        selectedBtn.style.borderColor = '#10b981';
        selectedBtn.style.fontWeight = '700';
    }

    populateProductsGrid();
}

// Helper functions to reset button styles
function resetPriceFilterButtons() {
    const allBtn = document.getElementById('price-all');
    const lowHighBtn = document.getElementById('price-low-high');
    const highLowBtn = document.getElementById('price-high-low');

    [allBtn, lowHighBtn, highLowBtn].forEach(btn => {
        if (btn) {
            btn.style.background = '#f8f9fa';
            btn.style.color = '#000000';
            btn.style.borderColor = '#e5e7eb';
            btn.style.fontWeight = '600';
        }
    });

    // Highlight 'All' button as default
    if (allBtn) {
        allBtn.style.background = '#10b981';
        allBtn.style.color = '#000000';
        allBtn.style.borderColor = '#10b981';
        allBtn.style.fontWeight = '700';
    }
}

function resetBarcodeFilterButtons() {
    const ascBtn = document.getElementById('sort-asc');
    const descBtn = document.getElementById('sort-desc');

    if (ascBtn) {
        ascBtn.style.background = '#3b82f6';
        ascBtn.style.color = '#000000';
        ascBtn.style.borderColor = '#3b82f6';
        ascBtn.style.fontWeight = '700';
    }

    if (descBtn) {
        descBtn.style.background = '#f8f9fa';
        descBtn.style.color = '#000000';
        descBtn.style.borderColor = '#e5e7eb';
        descBtn.style.fontWeight = '600';
    }
}

function populateCategories() {
    const categories = [...new Set(demoProducts.map(p => p.category))];
    const categoriesList = document.getElementById('categories-list');

    categoriesList.innerHTML = categories.map(category => `
        <div onclick="selectCategory('${category}')"
             style="padding: 12px; margin-bottom: 8px; cursor: pointer; border: 2px solid #666666; border-radius: 8px; font-size: 16px; font-weight: bold; transition: all 0.3s ease; background-color: #000000; color: #00ff00;"
             onmouseover="this.style.borderColor='#00ff00'; this.style.backgroundColor='#333333';"
             onmouseout="this.style.borderColor='#666666'; this.style.backgroundColor='#000000';"
             id="category-${category.replace(/[^a-zA-Z0-9]/g, '')}">
            ${category}
        </div>
    `).join('');
}

function selectCategory(category) {
    selectedCategory = category;
    selectedSubcategory = null;

    // Update category selection visual with black text always
    document.querySelectorAll('[id^="category-"]').forEach(el => {
        el.style.backgroundColor = '#f8f9fa';
        el.style.color = '#000000';
        el.style.borderColor = '#e5e7eb';
        el.style.fontWeight = '600';
    });

    if (category) {
        const selectedEl = document.getElementById(`category-${category.replace(/[^a-zA-Z0-9]/g, '')}`);
        if (selectedEl) {
            selectedEl.style.backgroundColor = '#667eea';
            selectedEl.style.color = '#000000';
            selectedEl.style.borderColor = '#667eea';
            selectedEl.style.fontWeight = '700';
        }
    } else {
        const allBtn = document.getElementById('category-all');
        if (allBtn) {
            allBtn.style.backgroundColor = '#667eea';
            allBtn.style.color = '#000000';
            allBtn.style.borderColor = '#667eea';
            allBtn.style.fontWeight = '700';
        }
    }

    // Show/hide subcategory filter bar and populate subcategories
    const subcategoryBar = document.getElementById('subcategory-filter-bar');
    if (category) {
        subcategoryBar.style.display = 'block';
        populateSubcategories();
    } else {
        subcategoryBar.style.display = 'none';
    }

    populateProductsGrid();
}

function populateSubcategories() {
    const subcategoriesList = document.getElementById('subcategories-list');

    if (!selectedCategory) {
        subcategoriesList.innerHTML = '';
        return;
    }

    const subcategories = [...new Set(
        demoProducts
            .filter(p => p.category === selectedCategory && p.subcategory)
            .map(p => p.subcategory)
    )];

    if (subcategories.length === 0) {
        subcategoriesList.innerHTML = '<div style="color: #9ca3af; font-size: 14px; padding: 8px 14px; font-style: italic;">No subcategories available</div>';
        return;
    }

    subcategoriesList.innerHTML = subcategories.map(subcategory => `
        <button onclick="selectSubcategory('${subcategory}')"
                id="subcategory-${subcategory.replace(/[^a-zA-Z0-9]/g, '')}"
                style="padding: 6px 14px; border: 2px solid #e5e7eb; background: #f8f9fa; color: #000000; border-radius: 20px; cursor: pointer; font-weight: 600; transition: all 0.3s ease; font-size: 14px;">
            ${subcategory}
        </button>
    `).join('');
}

function selectSubcategory(subcategory) {
    selectedSubcategory = subcategory;

    // Update subcategory selection visual with black text always
    document.querySelectorAll('[id^="subcategory-"]').forEach(el => {
        el.style.backgroundColor = '#f8f9fa';
        el.style.color = '#000000';
        el.style.borderColor = '#e5e7eb';
        el.style.fontWeight = '600';
    });

    // Update "All Items" button
    const allBtn = document.getElementById('subcategory-all');
    if (allBtn) {
        allBtn.style.backgroundColor = '#f8f9fa';
        allBtn.style.color = '#000000';
        allBtn.style.borderColor = '#e5e7eb';
        allBtn.style.fontWeight = '600';
    }

    if (subcategory) {
        const selectedEl = document.getElementById(`subcategory-${subcategory.replace(/[^a-zA-Z0-9]/g, '')}`);
        if (selectedEl) {
            selectedEl.style.backgroundColor = '#10b981';
            selectedEl.style.color = '#000000';
            selectedEl.style.borderColor = '#10b981';
            selectedEl.style.fontWeight = '700';
        }
    } else {
        // If subcategory is null, highlight "All Items" button
        if (allBtn) {
            allBtn.style.backgroundColor = '#10b981';
            allBtn.style.color = '#000000';
            allBtn.style.borderColor = '#10b981';
            allBtn.style.fontWeight = '700';
        }
    }

    populateProductsGrid();
}

function getFilteredProducts() {
    console.log(`POS - getFilteredProducts called. Category: ${selectedCategory}, subcategory: ${selectedSubcategory}, sort: ${sortOrder}, price: ${priceFilter}`);
    console.log(`POS - Total products to filter: ${demoProducts.length}`);

    // Start with all products or filter by category
    let filtered;
    if (!selectedCategory) {
        console.log('POS - No category selected, using all products');
        filtered = [...demoProducts];
    } else {
        filtered = demoProducts.filter(p => p.category === selectedCategory);
        console.log(`POS - After category filter: ${filtered.length} products`);
    }

    // Apply subcategory filter
    if (selectedSubcategory) {
        filtered = filtered.filter(p => p.subcategory === selectedSubcategory);
        console.log(`POS - After subcategory filter: ${filtered.length} products`);
    }

    // Apply sorting - either by barcode or by price, not both
    if (priceFilter === 'low-to-high') {
        filtered.sort((a, b) => a.price - b.price);
        console.log('POS - Applied price sort (Low to High)');
    } else if (priceFilter === 'high-to-low') {
        filtered.sort((a, b) => b.price - a.price);
        console.log('POS - Applied price sort (High to Low)');
    } else {
        // Apply barcode sorting when price filter is 'all'
        if (sortOrder === 'asc') {
            filtered.sort((a, b) => {
                const barcodeA = a.barcode || '';
                const barcodeB = b.barcode || '';
                return barcodeA.localeCompare(barcodeB, undefined, { numeric: true });
            });
            console.log('POS - Applied ascending sort by barcode');
        } else if (sortOrder === 'desc') {
            filtered.sort((a, b) => {
                const barcodeA = a.barcode || '';
                const barcodeB = b.barcode || '';
                return barcodeB.localeCompare(barcodeA, undefined, { numeric: true });
            });
            console.log('POS - Applied descending sort by barcode');
        }
    }

    console.log(`POS - Final filtered products: ${filtered.length}`);
    return filtered;
}

function getItemQuantityInCart(productName) {
    const item = cartItems.find(item => item.description === productName);
    return item ? item.quantity : 0;
}

function populateItems() {
    const itemsList = document.getElementById('items-list');
    const filteredProducts = getFilteredProducts();

    if (filteredProducts.length === 0) {
        itemsList.innerHTML = `
            <div style="color: #666666; text-align: center; padding: 48px; font-size: 18px;">
                ${selectedCategory ? "No items in this category" : "Select a category to view items"}
            </div>
        `;
        return;
    }

    itemsList.innerHTML = filteredProducts.map(product => {
        const quantityInCart = getItemQuantityInCart(product.name);
        return `
            <div style="display: flex; align-items: center; justify-content: space-between; padding: 16px; border-bottom: 2px solid #333333; transition: background-color 0.3s ease; ${quantityInCart > 0 ? 'background-color: #333333;' : ''}"
                 onmouseover="this.style.backgroundColor='#333333';"
                 onmouseout="this.style.backgroundColor='${quantityInCart > 0 ? '#333333' : 'transparent'}';">
                <!-- Product Info -->
                <div style="flex: 1;">
                    <div style="color: #00ff00; font-weight: bold; font-size: 18px; margin-bottom: 8px;">
                        ${product.name}
                    </div>
                    <div style="color: #ff0000; font-weight: 900; font-size: 20px; margin-bottom: 4px;">
                        $${formatCurrency(product.price)}
                    </div>
                    ${product.subcategory ? `<div style="color: #666666; font-size: 14px;">${product.subcategory}</div>` : ''}
                </div>

                <!-- Quantity and Controls -->
                <div style="display: flex; align-items: center; gap: 12px;">
                    ${quantityInCart > 0 ? `
                        <div style="background-color: #00ff00; color: #000000; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; font-size: 14px; font-weight: 900;">
                            ${quantityInCart}
                        </div>
                    ` : ''}
                    <button onclick="removeItemFromModal('${product.name}')"
                            ${quantityInCart === 0 ? 'disabled' : ''}
                            style="width: 48px; height: 48px; padding: 0; font-size: 14px; font-weight: 900; cursor: ${quantityInCart > 0 ? 'pointer' : 'not-allowed'}; border-radius: 4px; border: 2px solid; background-color: ${quantityInCart > 0 ? '#660000' : '#333333'}; color: ${quantityInCart > 0 ? '#ff0000' : '#666666'}; border-color: ${quantityInCart > 0 ? '#ff0000' : '#666666'}; transition: all 0.3s ease;"
                            ${quantityInCart > 0 ? 'onmouseover="this.style.backgroundColor=\'#990000\';" onmouseout="this.style.backgroundColor=\'#660000\';"' : ''}>
                        −
                    </button>
                    <button onclick="addItemFromModal('${product.name}')"
                            style="width: 48px; height: 48px; padding: 0; font-size: 14px; font-weight: 900; cursor: pointer; border-radius: 4px; border: 2px solid #00ff00; background-color: #006600; color: #00ff00; transition: all 0.3s ease;"
                            onmouseover="this.style.backgroundColor='#009900';" onmouseout="this.style.backgroundColor='#006600';">
                        ✓
                    </button>
                </div>
            </div>
        `;
    }).join('');
}

function addItemFromModal(productName) {
    const product = demoProducts.find(p => p.name === productName);
    if (product) {
        addItemToCart(product);
        populateProductsGrid(); // Refresh the products grid to update quantities
        updateModalFooter();
    }
}

function removeItemFromModal(productName) {
    const product = demoProducts.find(p => p.name === productName);
    if (product) {
        removeItemFromCart(product);
        populateProductsGrid(); // Refresh the products grid to update quantities
        updateModalFooter();
    }
}

function updateModalFooter() {
    const footer = document.getElementById('modal-footer');
    if (footer) {
        footer.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div style="color: #374151; font-weight: 600; font-size: 16px;">
                    Items in Cart: <span style="color: #667eea; font-size: 20px; font-weight: 700;">${cartItems.length}</span>
                </div>
                <div style="color: #374151; font-weight: 600; font-size: 16px;">
                    Total: <span style="color: #10b981; font-size: 24px; font-weight: 700;">$${formatCurrency(calculateTotal())}</span>
                </div>
            </div>
        `;
    }
}

function populateProductsGrid() {
    const productsGrid = document.getElementById('products-grid');
    const noProducts = document.getElementById('no-products');

    console.log(`POS - populateProductsGrid called. Grid element exists: ${!!productsGrid}`);
    console.log(`POS - Total products available: ${demoProducts.length}`);

    if (!productsGrid) {
        console.log('POS - Products grid element not found, modal may not be open');
        return;
    }

    const filteredProducts = getFilteredProducts();

    console.log(`POS - Populating product grid with ${filteredProducts.length} filtered products (total: ${demoProducts.length})`);
    console.log('POS - Sample filtered product:', filteredProducts[0]);

    if (filteredProducts.length === 0) {
        productsGrid.style.display = 'none';
        if (noProducts) {
            noProducts.style.display = 'block';
        }
        console.log('POS - No products to display in grid');
        return;
    }

    productsGrid.style.display = 'grid';
    noProducts.style.display = 'none';

    productsGrid.innerHTML = filteredProducts.map(product => {
        const quantityInCart = getItemQuantityInCart(product.name);
        return `
            <div style="background: white; border: 1px solid #e5e7eb; border-radius: 12px; overflow: hidden; transition: all 0.3s ease; box-shadow: 0 2px 8px rgba(0,0,0,0.1); ${quantityInCart > 0 ? 'border-color: #667eea; box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);' : ''}" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 8px 25px rgba(0,0,0,0.15)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='${quantityInCart > 0 ? '0 4px 12px rgba(102, 126, 234, 0.15)' : '0 2px 8px rgba(0,0,0,0.1)'}'">
                <!-- Product Image -->
                <div style="position: relative; height: 200px; background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%); display: flex; align-items: center; justify-content: center; overflow: hidden;">
                    <img src="${product.image}" alt="${product.name}" style="width: 100%; height: 100%; object-fit: cover;" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                    <div style="display: none; width: 100%; height: 100%; align-items: center; justify-content: center; background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%); color: #9ca3af; font-size: 48px;">📦</div>
                    ${quantityInCart > 0 ? `
                        <div style="position: absolute; top: 12px; right: 12px; background: #667eea; color: white; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; font-size: 14px; font-weight: 700; box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);">
                            ${quantityInCart}
                        </div>
                    ` : ''}
                </div>

                <!-- Product Info -->
                <div style="padding: 16px;">
                    <h3 style="font-size: 16px; font-weight: 600; color: #1f2937; margin: 0 0 8px 0; line-height: 1.4; height: 44px; overflow: hidden; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical;">${product.name}</h3>
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                        <span style="font-size: 20px; font-weight: 700; color: #10b981;">$${formatCurrency(product.price)}</span>
                        <span style="font-size: 12px; color: #6b7280; background: #f3f4f6; padding: 4px 8px; border-radius: 12px;">${product.subcategory || product.category}</span>
                    </div>

                    <!-- Action Buttons -->
                    <div style="display: flex; gap: 8px; align-items: center;">
                        <button onclick="removeItemFromModal('${product.name}')" ${quantityInCart === 0 ? 'disabled' : ''} style="flex: 1; padding: 8px; border: 2px solid ${quantityInCart > 0 ? '#ef4444' : '#e5e7eb'}; background: ${quantityInCart > 0 ? '#fef2f2' : '#f9fafb'}; color: ${quantityInCart > 0 ? '#ef4444' : '#9ca3af'}; border-radius: 8px; cursor: ${quantityInCart > 0 ? 'pointer' : 'not-allowed'}; font-weight: 600; transition: all 0.3s ease; display: flex; align-items: center; justify-content: center;" ${quantityInCart > 0 ? `onmouseover="this.style.background='#fee2e2'" onmouseout="this.style.background='#fef2f2'"` : ''}>
                            <span style="font-size: 18px;">−</span>
                        </button>
                        <button onclick="addItemFromModal('${product.name}')" style="flex: 2; padding: 10px; border: 2px solid #10b981; background: #10b981; color: white; border-radius: 8px; cursor: pointer; font-weight: 600; transition: all 0.3s ease; display: flex; align-items: center; justify-content: center; gap: 6px;" onmouseover="this.style.background='#059669'" onmouseout="this.style.background='#10b981'">
                            <span style="font-size: 16px;">+</span>
                            <span>Add to Cart</span>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }).join('');
}

// Quick Action Modal Functions
function showQuickActionModal() {
    const modal = document.createElement('div');
    modal.className = 'quick-action-modal show';
    modal.id = 'quick-action-modal';

    modal.innerHTML = `
        <div class="quick-action-modal-content">
            <div class="quick-action-modal-header">
                <h2 class="quick-action-modal-title">Quick Actions</h2>
            </div>
            <div class="quick-action-modal-body">
                <div class="quick-action-grid">
                    <button class="quick-action-btn" onclick="handlePrintDailySaleReport()">
                        Print Daily Sale Report
                    </button>
                    <button class="quick-action-btn" onclick="handlePrintShiftPerformanceReport()">
                        Print Shift Performance Report
                    </button>
                    <button class="quick-action-btn" onclick="handleAttendance()">
                        Attendance
                    </button>
                    <button class="quick-action-btn" onclick="handleSettings()">
                        Settings
                    </button>
                    <button class="quick-action-btn" onclick="handleEndShift()">
                        End Shift
                    </button>
                </div>
                <button class="quick-action-close-btn" onclick="closeQuickActionModal()">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                    </svg>
                    Close
                </button>
            </div>
        </div>
    `;

    // Add click outside to close functionality
    modal.addEventListener('click', function(event) {
        if (event.target === modal) {
            closeQuickActionModal();
        }
    });

    document.body.appendChild(modal);
}

function closeQuickActionModal() {
    const modal = document.getElementById('quick-action-modal');
    if (modal) {
        modal.remove();
    }
}

// Quick Action Handler Functions
function handlePrintDailySaleReport() {
    console.log('Opening Daily Sale Report options...');
    closeQuickActionModal();
    showDailySalesReportModal();
}

// Daily Sales Report Modal with Email Option
function showDailySalesReportModal() {
    const modal = document.createElement('div');
    modal.className = 'quick-action-modal show';
    modal.id = 'daily-report-modal';

    modal.innerHTML = `
        <div class="quick-action-modal-content">
            <div class="quick-action-modal-header">
                <h2 class="quick-action-modal-title">📊 Daily Sales Report</h2>
            </div>
            <div class="quick-action-modal-body">
                <div class="report-options">
                    <div class="date-selection">
                        <label for="report-date">Select Date:</label>
                        <input type="date" id="report-date" value="${new Date().toISOString().split('T')[0]}">
                    </div>

                    <div class="action-buttons">
                        <button class="report-action-btn email-btn" onclick="emailDailySalesReportFromPOS()">
                            📧 Email Report
                        </button>
                        <button class="report-action-btn print-btn" onclick="printDailySalesReportFromPOS()">
                            🖨️ Print Report
                        </button>
                        <button class="report-action-btn view-btn" onclick="viewDailySalesReportFromPOS()">
                            👁️ View Report
                        </button>
                    </div>
                </div>
                <button class="quick-action-close-btn" onclick="closeDailySalesReportModal()">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                    </svg>
                    Close
                </button>
            </div>
        </div>
    `;

    // Add click outside to close functionality
    modal.addEventListener('click', function(event) {
        if (event.target === modal) {
            closeDailySalesReportModal();
        }
    });

    document.body.appendChild(modal);
}

function closeDailySalesReportModal() {
    const modal = document.getElementById('daily-report-modal');
    if (modal) {
        modal.remove();
    }
}

function handlePrintShiftPerformanceReport() {
    console.log('Opening Shift Performance Report options...');
    closeQuickActionModal();
    showShiftPerformanceReportModal();
}

// Shift Performance Report Modal with Email Option
function showShiftPerformanceReportModal() {
    const modal = document.createElement('div');
    modal.className = 'quick-action-modal show';
    modal.id = 'shift-report-modal';

    modal.innerHTML = `
        <div class="quick-action-modal-content">
            <div class="quick-action-modal-header">
                <h2 class="quick-action-modal-title">📈 Shift Performance Report</h2>
            </div>
            <div class="quick-action-modal-body">
                <div class="report-options">
                    <div class="shift-info">
                        <p><strong>Current Shift:</strong> <span id="current-shift-info">Loading...</span></p>
                        <p><strong>Operator:</strong> <span id="current-operator-info">Loading...</span></p>
                    </div>

                    <div class="action-buttons">
                        <button class="report-action-btn email-btn" onclick="emailShiftPerformanceReportFromPOS()">
                            📧 Email Report
                        </button>
                        <button class="report-action-btn print-btn" onclick="printShiftPerformanceReportFromPOS()">
                            🖨️ Print Report
                        </button>
                        <button class="report-action-btn view-btn" onclick="viewShiftPerformanceReportFromPOS()">
                            👁️ View Report
                        </button>
                    </div>
                </div>
                <button class="quick-action-close-btn" onclick="closeShiftPerformanceReportModal()">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                    </svg>
                    Close
                </button>
            </div>
        </div>
    `;

    // Add click outside to close functionality
    modal.addEventListener('click', function(event) {
        if (event.target === modal) {
            closeShiftPerformanceReportModal();
        }
    });

    document.body.appendChild(modal);

    // Load current shift info
    loadCurrentShiftInfo();
}

function closeShiftPerformanceReportModal() {
    const modal = document.getElementById('shift-report-modal');
    if (modal) {
        modal.remove();
    }
}

async function loadCurrentShiftInfo() {
    try {
        const currentUser = await ipcRenderer.invoke('get-current-user');
        const shiftInfoElement = document.getElementById('current-shift-info');
        const operatorInfoElement = document.getElementById('current-operator-info');

        if (shiftInfoElement && operatorInfoElement) {
            if (currentUser && currentUser.current_shift) {
                const shift = currentUser.current_shift;
                const startTime = new Date(shift.shift_start_time).toLocaleString();
                shiftInfoElement.textContent = `${shift.shift_id} (Started: ${startTime})`;
                operatorInfoElement.textContent = currentUser.name || currentUser.username;
            } else {
                shiftInfoElement.textContent = 'No active shift';
                operatorInfoElement.textContent = currentUser?.name || currentUser?.username || 'Unknown';
            }
        }
    } catch (error) {
        console.error('Error loading shift info:', error);
        const shiftInfoElement = document.getElementById('current-shift-info');
        const operatorInfoElement = document.getElementById('current-operator-info');
        if (shiftInfoElement) shiftInfoElement.textContent = 'Error loading shift info';
        if (operatorInfoElement) operatorInfoElement.textContent = 'Error loading operator info';
    }
}

// Daily Sales Report Action Functions
async function emailDailySalesReportFromPOS() {
    const reportDate = document.getElementById('report-date')?.value;
    if (!reportDate) {
        alert('Please select a date for the report.');
        return;
    }

    // Show loading state
    const emailBtn = event.target;
    const originalText = emailBtn.innerHTML;
    emailBtn.innerHTML = '📧 Sending...';
    emailBtn.disabled = true;

    try {
        console.log('📧 Generating and emailing daily sales report for:', reportDate);

        // Generate report data (same filters as admin panel)
        const filters = {
            dateFrom: reportDate,
            dateTo: reportDate,
            shift: 'both',
            saleType: 'all',
            operator: 'all'
        };

        // Get report data from main process
        const reportResult = await ipcRenderer.invoke('get-daily-sales-report', filters);

        if (!reportResult.success || !reportResult.reportData || reportResult.reportData.length === 0) {
            alert('No sales data found for the selected date.');
            return;
        }

        // Generate CSV content
        let csvContent = "Date,Sale Type,Day Cash,Day Debit,Day Credit,Day Total,Night Cash,Night Debit,Night Credit,Night Total,Total Cash,Total Debit,Total Credit,Total Amount,Location,Operator\n";

        reportResult.reportData.forEach(record => {
            csvContent += [
                record.invoice_date,
                record.sale_type,
                record.day_cash || 0,
                record.day_debit || 0,
                record.day_credit || 0,
                record.day_total || 0,
                record.night_cash || 0,
                record.night_debit || 0,
                record.night_credit || 0,
                record.night_total || 0,
                record.total_cash || 0,
                record.total_debit || 0,
                record.total_credit || 0,
                record.total_amount || 0,
                record.location_name,
                record.operator_name
            ].join(',') + '\n';
        });

        // Send email via IPC
        const emailResult = await ipcRenderer.invoke('send-daily-sales-report-email', {
            reportData: reportResult.reportData,
            csvContent: csvContent,
            filters: filters
        });

        if (emailResult.success) {
            alert(`✅ Daily sales report emailed successfully!\n\nDate: ${reportDate}\nMessage ID: ${emailResult.messageId || 'N/A'}`);
            closeDailySalesReportModal();
        } else {
            alert(`❌ Failed to send email:\n\n${emailResult.message}`);
        }

    } catch (error) {
        console.error('Error sending daily sales report email:', error);
        alert(`❌ Error sending email:\n\n${error.message}`);
    } finally {
        // Restore button state
        emailBtn.innerHTML = originalText;
        emailBtn.disabled = false;
    }
}

async function printDailySalesReportFromPOS() {
    alert('Print functionality will be implemented soon!');
}

async function viewDailySalesReportFromPOS() {
    alert('View functionality will be implemented soon!');
}

// Shift Performance Report Action Functions
async function emailShiftPerformanceReportFromPOS() {
    // Show loading state
    const emailBtn = event.target;
    const originalText = emailBtn.innerHTML;
    emailBtn.innerHTML = '📧 Sending...';
    emailBtn.disabled = true;

    try {
        console.log('📧 Generating and emailing shift performance report...');

        // Get current shift data
        const shiftResult = await ipcRenderer.invoke('get-shift-sales-report', {
            operatorFilter: 'current',
            periodFilter: 'current'
        });

        if (!shiftResult.success || !shiftResult.data || !shiftResult.data.shift) {
            alert('No active shift found or no shift data available.');
            return;
        }

        const shiftData = shiftResult.data;
        const shift = shiftData.shift;
        const sales = shiftData.sales || [];
        const tickets = shiftData.tickets || [];
        const allTransactions = [...sales, ...tickets];

        // Generate CSV content (same as admin panel)
        let csvContent = "Shift Sales Report\n";
        csvContent += `Shift ID,${shift.shift_id}\n`;
        csvContent += `Operator,${shift.operator_name}\n`;
        csvContent += `Location,${shift.location_name}\n`;
        csvContent += `Start Time,${new Date(shift.shift_start_time).toLocaleString()}\n`;
        csvContent += `End Time,${shift.shift_end_time ? new Date(shift.shift_end_time).toLocaleString() : 'In Progress'}\n`;
        csvContent += `Status,${shift.status}\n\n`;

        // Calculate totals
        const saleTypeTotals = {
            sale: { amount: 0, count: 0 },
            theater: { amount: 0, count: 0 },
            deli: { amount: 0, count: 0 }
        };

        allTransactions.forEach(transaction => {
            const isTicket = transaction.record_type === 'ticket' || transaction.ticket_id;
            const type = isTicket ? 'theater' : (transaction.sale_type || 'sale');
            const amount = parseFloat(transaction.total_amount) || 0;

            saleTypeTotals[type].amount += amount;
            saleTypeTotals[type].count += 1;
        });

        const totalSales = saleTypeTotals.sale.amount + saleTypeTotals.theater.amount + saleTypeTotals.deli.amount;
        const totalTransactions = saleTypeTotals.sale.count + saleTypeTotals.theater.count + saleTypeTotals.deli.count;

        csvContent += "Summary\n";
        csvContent += `Total Sales,$${totalSales.toFixed(2)}\n`;
        csvContent += `Total Transactions,${totalTransactions}\n`;
        csvContent += `Regular Sales,$${saleTypeTotals.sale.amount.toFixed(2)}\n`;
        csvContent += `Regular Transactions,${saleTypeTotals.sale.count}\n`;
        csvContent += `Theater Sales,$${saleTypeTotals.theater.amount.toFixed(2)}\n`;
        csvContent += `Theater Transactions,${saleTypeTotals.theater.count}\n`;
        csvContent += `Deli Sales,$${saleTypeTotals.deli.amount.toFixed(2)}\n`;
        csvContent += `Deli Transactions,${saleTypeTotals.deli.count}\n\n`;

        if (allTransactions.length > 0) {
            csvContent += "Transaction Details\n";
            csvContent += "Transaction ID,Type,Amount,Items,Date,Time\n";

            // Sort transactions by date
            const sortedTransactions = allTransactions.sort((a, b) => {
                const dateA = new Date(a.sale_date || a.issued_at);
                const dateB = new Date(b.sale_date || b.issued_at);
                return dateB - dateA;
            });

            sortedTransactions.forEach(transaction => {
                const isTicket = transaction.record_type === 'ticket' || transaction.ticket_id;
                const transactionDate = new Date(transaction.sale_date || transaction.issued_at);
                const transactionId = transaction.sale_id || transaction.ticket_id;
                const transactionType = isTicket ? 'theater' : (transaction.sale_type || 'sale');
                const itemCount = isTicket ? '1' : (transaction.item_count || '0');

                csvContent += [
                    transactionId,
                    transactionType,
                    parseFloat(transaction.total_amount).toFixed(2),
                    itemCount,
                    transactionDate.toLocaleDateString(),
                    transactionDate.toLocaleTimeString()
                ].join(',') + '\n';
            });
        }

        // Send email via IPC
        const emailResult = await ipcRenderer.invoke('send-shift-sales-report-email', {
            shiftData: shiftData,
            csvContent: csvContent
        });

        if (emailResult.success) {
            alert(`✅ Shift performance report emailed successfully!\n\nShift ID: ${shift.shift_id}\nMessage ID: ${emailResult.messageId || 'N/A'}`);
            closeShiftPerformanceReportModal();
        } else {
            alert(`❌ Failed to send email:\n\n${emailResult.message}`);
        }

    } catch (error) {
        console.error('Error sending shift performance report email:', error);
        alert(`❌ Error sending email:\n\n${error.message}`);
    } finally {
        // Restore button state
        emailBtn.innerHTML = originalText;
        emailBtn.disabled = false;
    }
}

async function printShiftPerformanceReportFromPOS() {
    alert('Print functionality will be implemented soon!');
}

async function viewShiftPerformanceReportFromPOS() {
    alert('View functionality will be implemented soon!');
}

function handleAttendance() {
    console.log('Opening Attendance...');
    // TODO: Implement attendance functionality
    alert('Attendance functionality will be implemented soon!');
    closeQuickActionModal();
}

function handleSettings() {
    console.log('Opening Settings...');
    // TODO: Implement settings functionality
    alert('Settings functionality will be implemented soon!');
    closeQuickActionModal();
}

function handleEndShift() {
    console.log('Ending Shift...');
    closeQuickActionModal();
    endCurrentShift();
}

// Shift Management Functions
async function initializeShiftManagement() {
    if (!currentUser) {
        return;
    }

    try {
        const result = await ipcRenderer.invoke('get-current-shift');

        if (result.success && result.shift) {
            currentShift = result.shift;
            showShiftTimer();
            startShiftCountdown();
        } else if (result.success && !result.shift) {
            await startNewShift();
        }
    } catch (error) {
        console.error('Error initializing shift management:', error);
    }
}

async function startNewShift() {
    try {
        const shiftData = {
            duration_hours: 12
        };

        const result = await ipcRenderer.invoke('start-shift', shiftData);

        if (result.success) {
            currentShift = result.shift;
            showShiftTimer();
            startShiftCountdown();
        }
    } catch (error) {
        console.error('Error starting new shift:', error);
    }
}

function showShiftTimer() {
    const container = document.getElementById('shift-timer-container');
    if (container) {
        container.style.display = 'block';
    }
}

function hideShiftTimer() {
    const container = document.getElementById('shift-timer-container');
    if (container) {
        container.style.display = 'none';
    }
}

function startShiftCountdown() {
    // Clear any existing interval
    if (shiftUpdateInterval) {
        clearInterval(shiftUpdateInterval);
    }

    // Update immediately
    updateShiftDisplay();

    // Update every minute
    shiftUpdateInterval = setInterval(updateShiftDisplay, 60000);
}

function updateShiftDisplay() {
    if (!currentShift) {
        return;
    }

    try {
        if (!currentShift.shift_end_time) {
            return;
        }

        const now = dayjs();
        const endTime = dayjs(currentShift.shift_end_time);

        if (!endTime.isValid()) {
            return;
        }

        const remainingMs = endTime.diff(now);
        const remainingMinutes = Math.max(0, Math.floor(remainingMs / (1000 * 60)));

        currentShift.remaining_minutes = remainingMinutes;

        const hours = Math.floor(remainingMinutes / 60);
        const minutes = remainingMinutes % 60;

        if (isNaN(hours) || isNaN(minutes)) {
            return;
        }

        const timeString = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:00`;

        const timerElement = document.getElementById('shift-timer');
        const warningElement = document.getElementById('shift-warning');

        if (timerElement) {
            timerElement.textContent = timeString;

            timerElement.className = 'shift-timer';
            if (remainingMinutes <= 0) {
                timerElement.classList.add('expired');
                timerElement.textContent = 'EXPIRED';
                showShiftEndModal();
            } else if (remainingMinutes <= 30) {
                timerElement.classList.add('warning');
                if (warningElement) {
                    warningElement.style.display = 'block';
                }
            } else {
                if (warningElement) {
                    warningElement.style.display = 'none';
                }
            }
        }

        ipcRenderer.invoke('update-shift-time', remainingMinutes);

    } catch (error) {
        console.error('Error updating shift display:', error);
    }
}

function showShiftEndModal() {
    // Clear the countdown interval
    if (shiftUpdateInterval) {
        clearInterval(shiftUpdateInterval);
    }

    // Show modal asking user to end shift
    const modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10000;
    `;

    modal.innerHTML = `
        <div style="
            background-color: #1a1a1a;
            border: 2px solid #ff0000;
            border-radius: 12px;
            padding: 32px;
            text-align: center;
            color: #ffffff;
            max-width: 500px;
            box-shadow: 0 0 30px rgba(255, 0, 0, 0.5);
        ">
            <h2 style="color: #ff0000; margin-bottom: 16px; font-size: 28px;">⏰ SHIFT EXPIRED</h2>
            <p style="margin-bottom: 24px; font-size: 18px;">Your 12-hour shift has ended. Please end your shift to continue.</p>
            <div style="display: flex; gap: 16px; justify-content: center;">
                <button onclick="endCurrentShift()" style="
                    background-color: #ff0000;
                    color: white;
                    border: none;
                    padding: 12px 24px;
                    border-radius: 6px;
                    font-size: 16px;
                    font-weight: bold;
                    cursor: pointer;
                ">End Shift</button>
                <button onclick="extendShift()" style="
                    background-color: #666666;
                    color: white;
                    border: none;
                    padding: 12px 24px;
                    border-radius: 6px;
                    font-size: 16px;
                    font-weight: bold;
                    cursor: pointer;
                ">Extend (+1 Hour)</button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
}

async function endCurrentShift() {
    try {
        const shiftSummary = {
            total_sales: await calculateShiftSales(),
            total_transactions: await calculateShiftTransactions()
        };

        const result = await ipcRenderer.invoke('end-shift', shiftSummary);

        if (result.success) {
            currentShift = null;
            hideShiftTimer();

            if (shiftUpdateInterval) {
                clearInterval(shiftUpdateInterval);
            }

            const modal = document.querySelector('div[style*="position: fixed"]');
            if (modal) {
                modal.remove();
            }

            await startNewShift();
        } else {
            alert('Failed to end shift: ' + result.message);
        }
    } catch (error) {
        alert('Error ending shift: ' + error.message);
    }
}

function extendShift() {
    // Extend shift by 1 hour
    if (currentShift) {
        const endTime = new Date(currentShift.shift_end_time);
        endTime.setHours(endTime.getHours() + 1);
        currentShift.shift_end_time = endTime.toISOString();

        // Remove modal
        const modal = document.querySelector('div[style*="position: fixed"]');
        if (modal) {
            modal.remove();
        }

        // Restart countdown
        startShiftCountdown();


    }
}

async function calculateShiftSales() {
    if (!currentShift) {
        return 0;
    }

    try {
        const result = await ipcRenderer.invoke('get-shift-totals', currentShift.shift_id);
        if (result.success) {
            return result.data.total_sales || 0;
        }
    } catch (error) {
        console.error('Error calculating shift sales:', error);
    }
    return 0;
}

async function calculateShiftTransactions() {
    if (!currentShift) {
        return 0;
    }

    try {
        const result = await ipcRenderer.invoke('get-shift-totals', currentShift.shift_id);
        if (result.success) {
            return result.data.total_transactions || 0;
        }
    } catch (error) {
        console.error('Error calculating shift transactions:', error);
    }
    return 0;
}
